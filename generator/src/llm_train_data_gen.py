import json
from prompts.system import train_system, user_template, assistant_template
from converter.checkbox_mask import build_wrong_blocks
import os

def load_json_file(file_path):
    with open(file_path,"r", encoding="utf-8") as f:
        data = json.load(f)
    return data

def build_llm_data(data, store_path):
    train_data = []
    
    for d in data:
        image = d['file_name']
        text = d['masked_markdown']
        new_wrong_blocks = d['wrong_blocks']

        answer = json.dumps(new_wrong_blocks, indent=4, ensure_ascii=False)


        messages= [
            {"role": "user", "content": user_template.format(system=train_system, text=text)},
            {"role": "assistant", "content": assistant_template.format(answer=answer)}
        ]
        train = {
            "messages": messages,
            "images": [image]
        }
        train_data.append(train)
    with open(store_path, "w", encoding="utf-8") as f:
        json.dump(train_data, f, ensure_ascii=False, indent=4)


def get_train_test_data(data_dir, checkbox_length, complex=False):
    data_path = os.path.join(data_dir, "train_data.json")
    json_path = os.path.join(data_dir, "json_mapping.json")

    train_data_list = load_json_file(data_path)
    json_mapping = load_json_file(json_path)

    train_data, test_data = build_wrong_blocks(train_data_list, json_mapping, checkbox_length=checkbox_length, complex=complex)
    store_name = data_path.split("/")[-1].split(".")[0]
    
    train_store_path = os.path.join(data_dir, f"llm_data_train_{checkbox_length}.json")
    test_store_path = os.path.join(data_dir, f"llm_data_test_{checkbox_length}.json")
    
    build_llm_data(train_data, train_store_path)
    build_llm_data(test_data, test_store_path)

def gen_short_long_data(data_dir, complex):
    checkbox_length = ["short", "long"]
    for l in checkbox_length:
        get_train_test_data(data_dir, l, complex=complex)



if __name__ == "__main__":
    data_dir = "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data/llm_train_data"
    checkbox_length = ["short", "long"]
    for l in checkbox_length:
        get_train_test_data(data_dir, l)
