import json
from .scheme_proposal import scheme_proposal

json_schema_for_table_gen = {
    "type": "object",
    "properties": {
        "elements": {
            "type": "array",
            "items": {
                "oneOf": [
                    scheme_proposal["table_scheme"]
                ]
            }
        }
    },
    "definitions": {
        "checkbox": scheme_proposal["checkbox_scheme"],
        "list": scheme_proposal["list_scheme"],
    },
    "required": [
        "elements"
    ]
}

# 纯表格内容的示例
table_gen_example = json.dumps({
    "elements": [{
        "type": "table",
        "rows": [
            [
                {"content": "临床项目及报告单位信息", "colspan": 4}
            ],
            [
                {"content": "医疗机构及专业名称"},
                {"content": "测试医院骨髓移植科"},
                {"content": "电话"},
                {"content": "022-12345675"}
            ],
            [
                {"content": "申报单位名称"},
                {"content": "凯诺医药"},
                {"content": "电话"},
                {"content": "+86-12345678"}
            ],
            [
                {"content": "临床研究方案名称"},
                {"content": "测试试验药品治疗肿瘤的临床试验方案", "colspan": 3}
            ],
            [
                {"content": "临床研究方案号"},
                {"content": "Clin-001", "colspan": 3}
            ],
            [
                {"content": "临床适应症"},
                {"content": "肿瘤", "colspan": 3}
            ],
            [
                {"content": "临床研究分类"},
                {"content": {
                    "key": "临床研究分类",
                    "options": [
                        {"option": "Ⅰ期", "checked": True},
                        {"option": "Ⅱ期", "checked": False}
                    ]
                }, "colspan": 3}
            ],
            [
                {"content": "报告者姓名"},
                {"content": "张三"},
                {"content": "所在国家"},
                {"content": "中国"}
            ]
        ]
    }]
}, ensure_ascii=False, indent=4)

table_gen_prompt = """请生成{industry}行业{topic}相关的表格内容，要求：
        1. 只输出表格内容（type=table），不要包含标题、段落等其他元素
        2. 表格中应包含文本、复选框等丰富内容
        3. 合理使用rowspan/colspan进行单元格合并

        输出格式必须严格遵循以下JSON Schema：
        {schema}

        示例输出：
        ```json
        {example}
        ```
        
        请输出你的结果：
        ```json/nothink"""