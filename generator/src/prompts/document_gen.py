import json
json_schema_for_doc_gen = {
  "title": "DocumentSchema",
  "type": "object",
  "required": ["topic", "documents"],
  "properties": {
    "topic": {
      "type": "string",
      "description": "当前生成文档的主题"
    },
    "documents": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "title": { "type": "string" },
          "sections": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "type": {
                  "type": "string",
                  "enum": [
                    "markdown",
                    "info",
                    "checkbox",
                    "table",
                    "confirm",
                    "code",
                    "list"
                  ]
                },
                "title": { "type": "string" },
                "content": {
                  "type": "object",
                  "properties": {
                    "text": { "type": "string" }
                  },
                  "additionalProperties": True
                },
                "fields": {
                  "type": "object",
                  "additionalProperties": True
                },
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "id": { "type": "string" },
                      "label": { "type": "string" },
                      "checked": { "type": "boolean" },
                      "question": { "type": "string" },
                      "options": {
                        "type": "array",
                        "items": {
                          "type": "object",
                          "properties": {
                            "id": { "type": "string" },
                            "label": { "type": "string" },
                            "checked": { "type": "boolean" }
                          },
                          "required": ["id", "label", "checked"]
                        }
                      }
                    },
                    "required": {
                      "oneOf": [
                        { "required": ["id", "label", "checked"] },
                        { "required": ["question", "options"] }
                      ]
                    }
                  }
                },
                "headers": {
                  "type": "array",
                  "items": { "type": "string" }
                },
                "rows": {
                  "type": "array",
                  "items": {
                    "type": "array",
                    "items": { "type": "string" }
                  }
                },
                "language": { "type": "string" }
              },
              "required": ["type", "title"]
            }
          }
        },
        "required": ["title", "sections"]
      }
    }
  }
}

example = json.dumps({
  "topic": "患者症状记录",
  "documents": [
    {
      "title": "患者症状记录表",
      "sections": [
        {
          "type": "markdown",
          "title": "文档说明",
          "content": {
            "text": "本记录表用于收集和整理患者当前的主要症状，以便医疗人员快速评估患者的健康状况。"
          }
        },
        {
          "type": "checkbox",
          "title": "主要症状选择",
          "items": [
            {
              "id": "symptom1",
              "label": "头痛",
              "checked": True
            },
            {
              "id": "symptom2",
              "label": "发热",
              "checked": False
            },
            {
              "id": "symptom3",
              "label": "咳嗽",
              "checked": True
            },
            {
              "id": "symptom4",
              "label": "呼吸困难",
              "checked": False
            },
            {
              "id": "symptom5",
              "label": "胸痛",
              "checked": False
            },
            {
              "id": "symptom6",
              "label": "恶心呕吐",
              "checked": True
            },
            {
              "id": "symptom7",
              "label": "腹泻",
              "checked": False
            },
            {
              "id": "symptom8",
              "label": "乏力",
              "checked": False
            }
          ]
        },
        {
          "type": "checkbox",
          "title": "伴随症状选择",
          "items": [
            {
              "id": "accompanying1",
              "label": "头晕",
              "checked": False
            },
            {
              "id": "accompanying2",
              "label": "出汗",
              "checked": True
            },
            {
              "id": "accompanying3",
              "label": "食欲不振",
              "checked": False
            },
            {
              "id": "accompanying4",
              "label": "关节疼痛",
              "checked": True
            },
            {
              "id": "accompanying5",
              "label": "视力模糊",
              "checked": False
            }
          ]
        },
        {
          "type": "checkbox",
          "title": "近期接触史",
          "items": [
            {
              "id": "contact1",
              "label": "与确诊病人接触",
              "checked": True
            },
            {
              "id": "contact2",
              "label": "前往疫情高风险地区",
              "checked": False
            },
            {
              "id": "contact3",
              "label": "参与大型聚会",
              "checked": False
            }
          ]
        },
        {
          "type": "confirm",
          "title": "是否已接种疫苗？",
          "content": {
            "text": "请确认患者是否已接种新冠疫苗或其他相关疫苗。"
          }
        },
        {
          "type": "table",
          "title": "详细症状描述",
          "headers": ["症状", "持续时间（小时/天）", "严重程度"],
          "rows": [
            ["头痛", "", ""],
            ["发热", "", ""],
            ["咳嗽", "", ""]
          ]
        }
      ]
    }
  ]
}, ensure_ascii=False, indent=4)


document_gen_prompt = """我需要生成{industry}行业下面的{topic}相关的文档。
                第一步：你需要想出该文档的一个主题，即标题包含这个文档的全部内容。
                第二步：你需要对该文档进行内容的填充，在填充过程中你需要尽可能地使用checkbox这个类型来丰富你的文档内容，checkbox需要包含checked=True和False这两种类型。
                第三步：除了checkbox，文字信息和表格信息也是很重要的元素，请使用这两种类型来丰富你的文档内容。
                第四步：最后一步，你需要将以上两步的内容整合起来形成一个完整的文档，并将其以markdown形式返回给用户。
                
                你的输出结果应该以Json格式展示，并遵从以下schema格式。                
                {schema}
                
                示例输出：
                ```json
                {example}
                ```
                
                请输出你的结果：
                ```json/nothink"""