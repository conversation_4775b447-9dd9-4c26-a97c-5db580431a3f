import json
from .scheme_proposal import scheme_proposal
# 新版本的数据，更加复杂
json_schema_for_doc_gen = {
    "type": "object",
    "properties": {
        "elements": {
            "type": "array",
            "items": {
                "oneOf": [
                    scheme_proposal["paragraph_scheme"],
                    scheme_proposal["checkbox_scheme"],
                    scheme_proposal["table_scheme"],
                    scheme_proposal["list_scheme"],
                    scheme_proposal["signature_scheme"],
                    scheme_proposal["date_scheme"]
                ]
            }
        }
    },
    "definitions": {
        "checkbox": scheme_proposal["checkbox_scheme"],
        "list": scheme_proposal["list_scheme"],
    },
    "required": [
        "elements"
    ]
}

example = json.dumps({
    "elements": [
      {
        "type": "title",
        "level": 1,
        "content": "严重不良事件报告表(SAE)"
      },
      {
        "type": "paragraph",
        "content": "新药临床批准文号："
      },
      {
        "type": "checkbox",
        "key": "报告类型",
        "options": [
          {
            "option": "首次",
            "checked": True
          },
          {
            "option": "随访",
            "checked": False
          },
          {
            "option": "总结",
            "checked": False
          }
        ]
      },
      {
        "type": "date",
        "content": "2023-10-30"
      },
      {
        "type": "table",
        "rows": [
          [
            {
              "content": "临床项目及报告单位信息",
              "colspan": 4
            }
          ],
          [
            {
              "content": "医疗机构及专业名称"
            },
            {
              "content": "测试医院骨髓移植科"
            },
            {
              "content": "电话"
            },
            {
              "content": "022-12345675"
            }
          ],
          [
            {
              "content": "申报单位名称"
            },
            {
              "content": "凯诺医药"
            },
            {
              "content": "电话"
            },
            {
              "content": "+86-12345678"
            }
          ],
          [
            {
              "content": "临床研究方案名称"
            },
            {
              "content": "测试试验药品治疗肿瘤的临床试验方案",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床研究方案号"
            },
            {
              "content": "Clin-001",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床适应症"
            },
            {
              "content": "肿瘤",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床研究分类"
            },
            {
              "content": {
                "key": "临床研究分类",
                "options": [
                  {
                    "option": "Ⅰ期",
                    "checked": True
                  },
                  {
                    "option": "Ⅱ期",
                    "checked": False
                  },
                  {
                    "option": "Ⅲ期",
                    "checked": False
                  },
                  {
                    "option": "IV期",
                    "checked": False
                  },
                  {
                    "option": "生物等效性试验",
                    "checked": False
                  },
                  {
                    "option": "证类试验",
                    "checked": False
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "试验盲态情况"
            },
            {
              "content": {
                "key": "试验盲态情况",
                "options": [
                  {
                    "option": "盲态",
                    "checked": False
                  },
                  {
                    "option": "未破盲",
                    "checked": False
                  },
                  {
                    "option": "已破盲",
                    "checked": False
                  },
                  {
                    "option": "非盲态",
                    "checked": True
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "报告者信息",
              "colspan": 4
            }
          ],
          [
            {
              "content": "报告者姓名"
            },
            {
              "content": "张三"
            },
            {
              "content": "所在国家"
            },
            {
              "content": "中国"
            }
          ],
          [
            {
              "content": "职业"
            },
            {
              "content": "医生"
            },
            {
              "content": "电话"
            },
            {
              "content": ""
            }
          ],
          [
            {
              "content": "获知SAE时间"
            },
            {
              "content": {
                "key": "获知SAE时间",
                "options": [
                  {
                    "option": "首次获知时间",
                    "checked": True
                  },
                  {
                    "option": "随访信息获知时间",
                    "checked": False
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "受试者信息",
              "colspan": 4
            }
          ]
        ]
      }
    ]
  }, ensure_ascii=False, indent=4)


document_gen_prompt = """我需要生成{industry}行业下面的{topic}相关的文档。
                第一步：你需要想出该文档的一个主题，即标题包含这个文档的全部内容。
                第二步：你需要对该文档进行内容的填充，在填充过程中你需要尽可能地使用checkbox这个类型来丰富你的文档内容，checkbox需要包含checked=True和False这两种类型; 还有checkbox穿插在表格中。
                第三步：除了checkbox，文字信息和表格信息也是很重要的元素，请使用这两种类型来丰富你的文档内容。
                第四步：最后一步，你需要将以上两步的内容整合起来形成一个完整的文档，并将其以markdown形式返回给用户。
                
                你的输出结果应该以Json格式展示，并遵从以下schema格式。                
                {schema}
                
                示例输出：
                ```json
                {example}
                ```
                
                请输出你的结果：
                ```json/nothink"""