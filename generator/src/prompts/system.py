system = "You are a helpful assistant."

train_system = """你现在是信息处理专家，需要对数据进行修复，主要的任务是找出ocr识别结果中复选框勾选内容块与图片不符合的内容块。
 
## 不符合内容块的定义
图片中出现复选框内容为勾选，而OCR结果中没有勾选的部分。

## 标识符号
1. ["口", "□", "[ ]", "( )"]为非勾选的复选框标识符号。
2. ["☑", "[✓]", "[✔]", "☒", "(√)", "■"] 为勾选的复选框标识符号。
    
## 任务说明
1. wrong: 你需要认真比对ocr的识别结果和图片内容，并针对复选框的内容，找出与图片不符合的内容块。
2. line: 输出不符合内容块的行序号。
3. index: 若该内容块在该行重复出现，则输出该内容块在该行出现的次序，否则输出0。
4. refined: 修正之后的内容。

## 输出格式
你需要输出一个json，其中包含以下字段，请不要输出任何解释性的内容：
```json
[{
   "line": int,
   "index": int ,
   "wrong": string,
   "refined": string
]}```"""

user_template = """<image>{system}
## OCR 识别结果
```plaintext
{text}
```

请按要求找出与图片不符合的内容块，请输出：
```json"""


assistant_template = """```json
{answer}```"""
