
scheme_proposal = {
    "paragraph_scheme": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "paragraph"
                ]
            },
            "content": {
                "type": "string"
            }
        },
        "required": [
            "type",
            "content"
        ]
    },
    "checkbox_scheme": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "checkbox"
                ]
            },
            "key": {
                "type": "string"
            },
            "options": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "option": {
                            "type": "string"
                        },
                        "checked": {
                            "type": "boolean"
                        }
                    },
                    "required": [
                        "option",
                        "checked"
                    ]
                }
            }
        },
        "required": [
            "type",
            "key",
            "options"
        ]
    },
    "list_scheme": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "list"
                ]
            },
            "items": {
                "type": "array",
                "items": {
                    "type": "string"
                }
            }
        },
        "required": [
            "type",
            "items"
        ]
    },
    "table_scheme": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "table"
                ]
            },
            "rows": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "oneOf": [
                                    {
                                        "type": "string"
                                    },
                                    {
                                        "type": "object",
                                        "$ref": "#/definitions/checkbox"
                                    },
                                    {
                                        "type": "object",
                                        "$ref": "#/definitions/list"
                                    }
                                ]
                            },
                            "rowspan": {
                                "type": "integer",
                                "minimum": 1
                            },
                            "colspan": {
                                "type": "integer",
                                "minimum": 1
                            }
                        },
                        "required": [
                            "content"
                        ]
                    }
                }
            }
        },
        "required": [
            "type",
            "rows"
        ]
    },
    "signature_scheme": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "signature"
                ]
            },
            "content": {
                "type": "string"
            }
        },
        "required": [
            "type",
            "content"
        ]
    },
    "date_scheme": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "date"
                ]
            },
            "content": {
                "type": "string",
                "format": "date"
            }
        },
        "required": [
            "type",
            "content"
        ]
    }
}