import json
import random

checkbox_dict = {
    "markdown": {
        "selected": "- [x]",
        "unselected": "- [ ]"
    },
    "normal": {
        "selected": random.choice(["☑", "[✓]", "[✔]", "☒", "(√)","■"]),
        "unselected": "☐"
    }
}

unselect_dict = {
    "☑": "☐",
    "[✓]": "[ ]",
    "[✔]": "[ ]",
    "☒": "☐",
    "(√)": "( )",
    "■": "□",    
}

def get_checkbox_type(k=3):
    directional = random.choices(["horizontal", "vertical"],k=k)
    
    
    selected_list = random.sample(["☑", "[✓]", "[✔]", "☒", "(√)","■"], k=k)
    
    sampled_list = []
    
    for dir, selected in zip(directional, selected_list):
        sampled_list.append(
            {
                "direction": dir,
                "checkbox": {
                    "selected": selected,
                    "unselected": unselect_dict[selected],
                }
            }
        )
    return sampled_list

def get_checkbox_style():
    if random.random() < 0.5:
        return "markdown"
    else:
        return "normal"

def json_to_markdown(json_data, direction="horizontal", checkbox={"selected": "- [x]", "unselected": "- [ ]"}, style="markdown"):
    markdown = ""    

    for document in json_data.get("documents", []):
        markdown += f"# {document['title']}\n\n"
        
        for section in document.get("sections", []):
            if section["type"] == "markdown":
                markdown += f"## {section['title']}\n\n{section['content']['text']}\n\n"
            
            elif section["type"] == "checkbox":
                markdown += f"## {section['title']}\n\n"
                checkbox_list = []
                for item in section.get("items", []):
                    if item.get("checked"):
                        checkbox_list.append(f"{checkbox['selected']} {item['label']}")
                    else:
                        checkbox_list.append(f"{checkbox['unselected']} {item['label']}")
                    
                if style == "markdown":
                    markdown += "\n".join(checkbox_list) + "\n\n"
                elif direction == "horizontal":
                    markdown += "\t".join(checkbox_list) + "\n\n"
                else:
                    markdown += "\n".join(checkbox_list) + "\n\n"
                markdown += "\n"                
            elif section["type"] == "confirm":
                markdown += f"## {section['title']}\n\n{section['content']['text']}\n\n"
                
            elif section["type"] == "table":
                headers = section.get("headers", [])
                rows = section.get("rows", [])
                
                # Create table header
                markdown += f"## {section['title']}\n\n| {' | '.join(headers)} |\n"
                markdown += "| " + " | ".join(["---" for _ in headers]) + " |\n"
                
                # Add table rows
                for row in rows:
                    markdown += f"| {' | '.join(row)} |\n"
                markdown += "\n"
                
    return markdown

# Example usage with the provided JSON data
json_input = {
  "topic": "患者症状记录",
  "documents": [
    {
      "title": "患者症状记录表",
      "sections": [
        {
          "type": "markdown",
          "title": "文档说明",
          "content": {
            "text": "本记录表用于收集和整理患者当前的主要症状，以便医疗人员快速评估患者的健康状况。"
          }
        },
        {
          "type": "checkbox",
          "title": "主要症状选择",
          "items": [
            {
              "id": "symptom1",
              "label": "头痛",
              "checked": True
            },
            {
              "id": "symptom2",
              "label": "发热",
              "checked": False
            },
            {
              "id": "symptom3",
              "label": "咳嗽",
              "checked": True
            },
            {
              "id": "symptom4",
              "label": "呼吸困难",
              "checked": False
            },
            {
              "id": "symptom5",
              "label": "胸痛",
              "checked": False
            },
            {
              "id": "symptom6",
              "label": "恶心呕吐",
              "checked": True
            },
            {
              "id": "symptom7",
              "label": "腹泻",
              "checked": False
            },
            {
              "id": "symptom8",
              "label": "乏力",
              "checked": False
            }
          ]
        },
        {
          "type": "checkbox",
          "title": "伴随症状选择",
          "items": [
            {
              "id": "accompanying1",
              "label": "头晕",
              "checked": False
            },
            {
              "id": "accompanying2",
              "label": "出汗",
              "checked": True
            },
            {
              "id": "accompanying3",
              "label": "食欲不振",
              "checked": False
            },
            {
              "id": "accompanying4",
              "label": "关节疼痛",
              "checked": True
            },
            {
              "id": "accompanying5",
              "label": "视力模糊",
              "checked": False
            }
          ]
        },
        {
          "type": "checkbox",
          "title": "近期接触史",
          "items": [
            {
              "id": "contact1",
              "label": "与确诊病人接触",
              "checked": True
            },
            {
              "id": "contact2",
              "label": "前往疫情高风险地区",
              "checked": False
            },
            {
              "id": "contact3",
              "label": "参与大型聚会",
              "checked": False
            }
          ]
        },
        {
          "type": "confirm",
          "title": "是否已接种疫苗？",
          "content": {
            "text": "请确认患者是否已接种新冠疫苗或其他相关疫苗。"
          }
        },
        {
          "type": "table",
          "title": "详细症状描述",
          "headers": ["症状", "持续时间（小时/天）", "严重程度"],
          "rows": [
            ["头痛", "", ""],
            ["发热", "", ""],
            ["咳嗽", "", ""]
          ]
        }
      ]
    }
  ]
}

# Convert JSON to Markdown
# markdown_output = json_to_markdown(json_input, horizontal=True, checkbox={"selected": "[x]", "unselected": "[ ]"}, style="normal")
# print(markdown_output)