import json
import random
import jinja2

templates = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>A4 页面布局</title>
    <style>
        body {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            box-sizing: border-box;
            font-family: sans-serif;
            background-color: #f9f9f9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }

        /* 打印样式 */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background-color: white;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
{{body}}
</body>
</html>
"""

checkbox_dict = {
    "markdown": {
        "selected": "- [x]",
        "unselected": "- [ ]"
    },
    "normal": {
        "selected": random.choice(["☑", "[✓]", "[✔]", "☒", "(√)","■"]),
        "unselected": "☐"
    }
}

unselect_dict = {
    "☑": "☐",
    "[✓]": "[ ]",
    "[✔]": "[ ]",
    "☒": "☐",
    "(√)": "( )",
    "■": "□",    
}

def get_checkbox_type(k=3):
    directional = random.choices(["horizontal", "vertical"],k=k)
    
    
    selected_list = random.sample(["☑", "[✓]", "[✔]", "☒", "(√)","■"], k=k)
    
    sampled_list = []
    
    for dir, selected in zip(directional, selected_list):
        sampled_list.append(
            {
                "direction": dir,
                "checkbox": {
                    "selected": selected,
                    "unselected": unselect_dict[selected],
                }
            }
        )
    return sampled_list

def get_checkbox_style():
    if random.random() < 0.5:
        return "markdown"
    else:
        return "normal"

def json2html(json_data, direction="horizontal", checkbox={"selected": "- [x]", "unselected": "- [ ]"}, style="markdown"):
    elements = json_data.get('elements', [])
    
    html_parts = []

    for element in elements:

        type_ = element.get('type')

        if type_ == 'title':

            level = element.get('level', 1)
            content = element.get('content', '')
            html_parts.append(f'<h{level}>{content}</h{level}>')

        elif type_ == 'paragraph':

            content = element.get('content', '')

            html_parts.append(f'<p>{content}</p>')


        elif type_ == 'checkbox':
            key = element.get('key', '')
            options = element.get('options', [])
            checkbox_html = f'<div><strong>{key}:</strong>'

            for option in options:
                option_text = option.get('option', '')
                checked = "selected" if option.get('checked') else "unselected" 
                if direction == "horizontal":
                  checkbox_html += f'<span> {checkbox[checked]} {option_text}</span>'
                else:
                  checkbox_html += f'<div> {checkbox[checked]} {option_text}</div>'
                  
            checkbox_html += '</div>'
            html_parts.append(checkbox_html)

        elif type_ == 'list':

            items = element.get('items', [])

            list_html = '<ul>'
            for item in items:

                list_html += f'<li>{item}</li>'

            list_html += '</ul>'
            html_parts.append(list_html)

        elif type_ == 'table':
            rows = element.get('rows', [])
            table_html = '<table border="1">'

            for row in rows:
                table_html += '<tr>'

                for cell in row:
                    content = cell.get('content', '')
                    rowspan = cell.get('rowspan', 1)
                    colspan = cell.get('colspan', 1)
                    cell_content = ""


                    if isinstance(content, str):
                        cell_content = content

                    elif isinstance(content, dict) and 'options' in content:
                        # 检查pre，content是否包含key
                        if content['key'] not in table_html:
                            table_html += f'<td rowspan="1" colspan="1">{content["key"]}</td>'


                        
                        options = content.get('options', [])
                        for option in options:
                            option_text = option.get('option', '')
                            checked = "selected" if option.get('checked') else "unselected" 
                            if direction == "horizontal":
                              cell_content += f'<span> {checkbox[checked]} {option_text}</span>'
                            else:
                              cell_content += f'<div> {checkbox[checked]} {option_text}</div>'

                    elif isinstance(content, list):

                        cell_content += '<ul>'

                        for item in content:

                            cell_content += f'<li>{item}</li>'

                        cell_content += '</ul>'


                    table_html += f'<td rowspan="{rowspan}" colspan="{colspan}">{cell_content}</td>'

                table_html += '</tr>'
            table_html += '</table>'

            html_parts.append(table_html)

        elif type_ == 'signature':

            content = element.get('content', '')

            html_parts.append(f'<div><em>Signature: {content}</em></div>')


        elif type_ == 'date':
            content = element.get('content', '')
            html_parts.append(f'<div><em>Date: {content}</em></div>')

    return "\n".join(html_parts)

def body2html(body):
    html = jinja2.Template(templates).render(
        body=body
    )
    return html

# Example usage with the provided JSON data

if __name__ == "__main__":
    # Convert JSON to Markdown
    json_input ={
    "elements": [
      {
        "type": "title",
        "level": 1,
        "content": "严重不良事件报告表(SAE)"
      },
      {
        "type": "paragraph",
        "content": "新药临床批准文号："
      },
      {
        "type": "checkbox",
        "key": "报告类型",
        "options": [
          {
            "option": "首次",
            "checked": True
          },
          {
            "option": "随访",
            "checked": False
          },
          {
            "option": "总结",
            "checked": False
          }
        ]
      },
      {
        "type": "date",
        "content": "2023-10-30"
      },
      {
        "type": "table",
        "rows": [
          [
            {
              "content": "临床项目及报告单位信息",
              "colspan": 4
            }
          ],
          [
            {
              "content": "医疗机构及专业名称"
            },
            {
              "content": "测试医院骨髓移植科"
            },
            {
              "content": "电话"
            },
            {
              "content": "022-12345675"
            }
          ],
          [
            {
              "content": "申报单位名称"
            },
            {
              "content": "凯诺医药"
            },
            {
              "content": "电话"
            },
            {
              "content": "+86-12345678"
            }
          ],
          [
            {
              "content": "临床研究方案名称"
            },
            {
              "content": "测试试验药品治疗肿瘤的临床试验方案",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床研究方案号"
            },
            {
              "content": "Clin-001",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床适应症"
            },
            {
              "content": "肿瘤",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床研究分类"
            },
            {
              "content": {
                "key": "临床研究分类",
                "options": [
                  {
                    "option": "Ⅰ期",
                    "checked": True
                  },
                  {
                    "option": "Ⅱ期",
                    "checked": False
                  },
                  {
                    "option": "Ⅲ期",
                    "checked": False
                  },
                  {
                    "option": "IV期",
                    "checked": False
                  },
                  {
                    "option": "生物等效性试验",
                    "checked": False
                  },
                  {
                    "option": "证类试验",
                    "checked": False
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "试验盲态情况"
            },
            {
              "content": {
                "key": "试验盲态情况",
                "options": [
                  {
                    "option": "盲态",
                    "checked": False
                  },
                  {
                    "option": "未破盲",
                    "checked": False
                  },
                  {
                    "option": "已破盲",
                    "checked": False
                  },
                  {
                    "option": "非盲态",
                    "checked": True
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "报告者信息",
              "colspan": 4
            }
          ],
          [
            {
              "content": "报告者姓名"
            },
            {
              "content": "张三"
            },
            {
              "content": "所在国家"
            },
            {
              "content": "中国"
            }
          ],
          [
            {
              "content": "职业"
            },
            {
              "content": "医生"
            },
            {
              "content": "电话"
            },
            {
              "content": ""
            }
          ],
          [
            {
              "content": "获知SAE时间"
            },
            {
              "content": {
                "key": "获知SAE时间",
                "options": [
                  {
                    "option": "首次获知时间",
                    "checked": True
                  },
                  {
                    "option": "随访信息获知时间",
                    "checked": False
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "受试者信息",
              "colspan": 4
            }
          ]
        ]
      }
    ]
  }

    markdown_output = json2html(json_input, direction="horizontal", checkbox={"selected": "[x]", "unselected": "[ ]"}, style="normal")

    html = body2html(markdown_output)
    print(html)