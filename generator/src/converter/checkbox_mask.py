import json
import markdown
import random
# 最后的数据格式
"""
[
    {
        "file_name": "",
        "fields": [
            {
                "field_name": "",
                "field_value": "",
                "field_type": ""
            }
        ],
        "markdown": "",
        "html": "",
        "wrong_blocks": [
            {"line": int, "order": int, "text": str}
        ],
    }
]
"""
checkbox_mapping = {
    "☑": "☐",
    "- [x]": "- [ ]",
    "[✓]": "[ ]",
    "[✔]": "[ ]",
    "☒": "☐",
    "(√)": "( )",
    "■": "□",
}


def get_checkbox_type(html):
    for k, v in checkbox_mapping.items():
        if k in html and v in html:
            return k, v
    
    for k, v in checkbox_mapping.items():
        if v in html or k in html:
            return k, v
import random
random.seed(1)


def build_complex_new_data(json_data, checkbox_type, checkbox_length="short"):
    checkbox_type = {
        "selected": checkbox_type[0],
        "unselected": checkbox_type[1],
    }
    if checkbox_length == "short":
        wrong_block_probability = [0.65, 0.45, 0.25, 0.05, 0.05, 0]
    else:
        wrong_block_probability = [0.65, 0.65, 0.65, 0.65, 0.65, 0.3, 0.1, 0] 

    need_wrong_block = random.choices([True, False], weights=[1, 0])[0]
    h_template = "<span> {mark} {text}</span>"
    v_template = "<div> {mark} {text}</div>"
    template_mapping = {
        "horizontal": h_template,
        "vertical": v_template,
    }
    direction = random.choice(list(template_mapping.keys()))

    html_parts = []
    wrong_blocks = []
    refined_mapping = {}
    wrong_block_list = []

    elements = json_data.get("elements", [])

    for element in elements:

        type_ = element.get('type')

        if type_ == 'title':

            level = element.get('level', 1)
            content = element.get('content', '')
            html_parts.append(f'<h{level}>{content}</h{level}>')

        elif type_ == 'paragraph':

            content = element.get('content', '')

            html_parts.append(f'<p>{content}</p>')


        elif type_ == 'checkbox':
            key = element.get('key', '')
            options = element.get('options', [])
            checkbox_html = f'<div><strong>{key}:</strong>'

            for option in options:
                option_text = option.get('option', '')
                checked = "selected" if option.get('checked') else "unselected" 
                template = template_mapping[direction]
                
                if random.random() < wrong_block_probability[len(wrong_blocks)] and checked == "selected": # 选中的才有概率会被 masked 掉
                    wrong = template.format(mark=checkbox_type['unselected'], text=option_text)
                    refined = template.format(mark=checkbox_type['selected'], text=option_text)
                    checkbox_html += template.format(mark=checkbox_type["unselected"], text=option_text)
                    wrong_block_list.append(
                        {
                            "line": len(html_parts) ,
                            "index": checkbox_html.count(wrong) - 1,
                            "wrong": f"{checkbox_type['unselected']} {option_text}",
                            "refined": f"{checkbox_type['selected']} {option_text}"
                        }
                    )
                    
                else:
                    checkbox_html += template.format(mark=checkbox_type[checked], text=option_text)

                  
            checkbox_html += '</div>'
            html_parts.append(checkbox_html)

        elif type_ == 'list':

            items = element.get('items', [])

            list_html = '<ul>'
            for item in items:

                list_html += f'<li>{item}</li>'

            list_html += '</ul>'
            html_parts.append(list_html)

        elif type_ == 'table':
            rows = element.get('rows', [])
            table_html = '<table border="1">'

            for row in rows:
                table_html += '<tr>'

                for cell in row:
                    content = cell.get('content', '')
                    rowspan = cell.get('rowspan', 1)
                    colspan = cell.get('colspan', 1)
                    cell_content = ""


                    if isinstance(content, str):
                        cell_content = content

                    elif isinstance(content, dict) and 'options' in content:
                        # 检查pre，content是否包含key
                        if content['key'] not in table_html:
                            table_html += f'<td rowspan="1" colspan="1">{content["key"]}</td>'


                        
                        options = content.get('options', [])
                        for option in options:
                            option_text = option.get('option', '')
                            checked = "selected" if option.get('checked') else "unselected" 
                            template = template_mapping[direction]
                            
                            if random.random() < wrong_block_probability[len(wrong_blocks)] and checked == "selected": # 选中的才有概率会被 masked 掉
                                wrong = template.format(mark=checkbox_type['unselected'], text=option_text)
                                refined = template.format(mark=checkbox_type['selected'], text=option_text)
                                cell_content += template.format(mark=checkbox_type["unselected"], text=option_text)
                                wrong_block_list.append(
                                    {
                                        "line": len(html_parts),
                                        "index": cell_content.count(wrong) - 1,
                                        "wrong": f"{checkbox_type['unselected']} {option_text}",
                                        "refined": f"{checkbox_type['selected']} {option_text}"
                                    }
                                )
                            else:
                                print(checkbox_type, checked)
                                cell_content += template.format(mark=checkbox_type[checked], text=option_text)

                    elif isinstance(content, list):

                        cell_content += '<ul>'

                        for item in content:

                            cell_content += f'<li>{item}</li>'

                        cell_content += '</ul>'


                    table_html += f'<td rowspan="{rowspan}" colspan="{colspan}">{cell_content}</td>'

                table_html += '</tr>'
            table_html += '</table>'

            html_parts.append(table_html)

        elif type_ == 'signature':

            content = element.get('content', '')

            html_parts.append(f'<div><em>Signature: {content}</em></div>')


        elif type_ == 'date':
            content = element.get('content', '')
            html_parts.append(f'<div><em>Date: {content}</em></div>')

    return wrong_block_list, "\n".join(html_parts), ""

def build_new_data(json_data, checkbox_type, checkbox_length="short"):
    if checkbox_length == "short":
        wrong_block_probability = [0.65, 0.45, 0.25, 0.05, 0.05, 0]
    else:
        wrong_block_probability = [0.65, 0.65, 0.65, 0.65, 0.65, 0.3, 0.1, 0] 

    need_wrong_block = random.choices([True, False], weights=[1, 0])[0]

    markdown_components = []    
    correct_markdown = []
    wrong_blocks = []
    refined_mapping = {}

    for document in json_data.get("documents", []):
        markdown_components.append(f"# {document['title']}")
        correct_markdown.append(f"# {document['title']}")

        
        for section in document.get("sections", []):
            if section["type"] == "markdown":
                markdown_components.append(f"## {section['title']}")
                markdown_components.append(f"{section['content']['text']}")

                correct_markdown.append(f"## {section['title']}")
                correct_markdown.append(f"{section['content']['text']}")
            
            elif section["type"] == "checkbox":
                markdown_components.append(f"## {section['title']}")
                correct_markdown.append(f"## {section['title']}")

                checkbox_list = []
                correct_checkbox_list = []
                for item in section.get("items", []):
                    if item.get("checked"):
                        checkbox_content = f"{checkbox_type[0]} {item['label']}"
                        correct_checkbox_list.append(checkbox_content)
                        # 增加随机数来进行修改内容
                        if random.random() < wrong_block_probability[len(wrong_blocks)] and need_wrong_block:
                            new_checkbox_content = f"{checkbox_type[1]} {item['label']}"
                            refined_mapping[new_checkbox_content] = checkbox_content
                            wrong_blocks.append(new_checkbox_content)
                            checkbox_content = new_checkbox_content

                        
                        checkbox_list.append(checkbox_content)

                    else:
                        checkbox_list.append(f"{checkbox_type[1]} {item['label']}")
                        correct_checkbox_list.append(f"{checkbox_type[1]} {item['label']}")
                
                if checkbox_type[0] == "- [x]":
                    markdown_components.append("\n".join(checkbox_list))
                    correct_markdown.append("\n".join(correct_checkbox_list))
                else:
                    markdown_components.append("\t".join(checkbox_list))
                    correct_markdown.append("\t".join(correct_checkbox_list))

                
                
            elif section["type"] == "confirm":
                markdown_components.append(f"## {section['title']}")
                markdown_components.append(f"{section['content']['text']}")

                correct_markdown.append(f"## {section['title']}")
                correct_markdown.append(f"{section['content']['text']}")
                
            elif section["type"] == "table":
                headers = section.get("headers", [])
                rows = section.get("rows", [])
                
                # Create table header
                markdown_components.append(f"## {section['title']}")
                markdown_components.append(f"| {' | '.join(headers)} |")
                markdown_components.append("| " + " | ".join(["---" for _ in headers]) + " |")

                correct_markdown.append(f"## {section['title']}")
                correct_markdown.append(f"| {' | '.join(headers)} |")
                correct_markdown.append("| " + " | ".join(["---" for _ in headers]) + " |")
                
                # Add table rows
                for row in rows:
                    markdown_components.append(f"| {' | '.join(row)} |")
                    correct_markdown.append(f"| {' | '.join(row)} |")

    
    markdown_content = "\n".join(markdown_components)
    correct_markdown_content = "\n".join(correct_markdown)

    # 这里转换一部分为html
    # if random.random() < 0.3:
    #     markdown_content = markdown.markdown(markdown_content)
    #     correct_markdown_content = markdown.markdown(correct_markdown_content)


    markdown_list = markdown_content.split("\n")
    correct_markdown_list = correct_markdown_content.split("\n")

    # print(len(wrong_blocks))

    wrong_block_list = []
    
    for block in wrong_blocks:
        for i, m in enumerate(markdown_list):
            if block in m:
                wrong_block_list.append(
                    {
                        "line": i,
                        "index": 0,
                        "wrong": block,
                        "refined": refined_mapping[block]
                    }
                )

    # wrong_blcok_list 去重
    deduplicated_list = []
    for w in wrong_block_list:
        ws = f"{w['line']}_{w['index']}_{w['wrong']}"
        match = False
        for d in deduplicated_list:
            d_line = f"{d['line']}_{d['index']}_{d['wrong']}"
            if ws == d_line:
                match = True
        
        if not match:
            deduplicated_list.append(w)
    # print(len(wrong_block_list), len(deduplicated_list), len(wrong_blocks))
    # 去掉一些badcase
    if len(wrong_blocks) != len(deduplicated_list):
        return [], correct_markdown_content, correct_markdown_content

    return deduplicated_list, markdown_content, correct_markdown_content

    

def build_wrong_blocks(train_data_list, json_mapping, checkbox_length="short", complex=False):
    train_data_index_list = [i for i in range(len(train_data_list)//4)]
    random.shuffle(train_data_index_list)
    train_index = train_data_index_list[int(len(train_data_index_list)*0.05):]
    from collections import defaultdict
    count_statistic = defaultdict(int)
    rebuild_train_data = []
    rebuild_test_data = []
    for i, train_data in enumerate(train_data_list):
        file_name = train_data['image_path']
        markdown = train_data['text']

        json_id = train_data['json_id']
        json_object = json_mapping[f'{json_id}']

        checkbox_type = get_checkbox_type(markdown)
        if checkbox_type == None:
            print(checkbox_type, markdown)
            continue
        if complex:
            wrong_block_list, masked_markdown, correct_markdown = build_complex_new_data(json_object, checkbox_type=checkbox_type, checkbox_length=checkbox_length)
        else:  
            wrong_block_list, masked_markdown, correct_markdown = build_new_data(json_object, checkbox_type=checkbox_type, checkbox_length=checkbox_length)
        if len(wrong_block_list) == 0: continue

        count_statistic[len(wrong_block_list)] += 1

        if int(json_id) in train_index:        
            rebuild_train_data.append(
                {
                    "file_name": file_name,
                    "correct_markdown": correct_markdown,
                    "masked_markdown": masked_markdown,
                    "wrong_blocks": wrong_block_list
                }
            )
        else:
            rebuild_test_data.append(
                {
                    "file_name": file_name,
                    "correct_markdown": correct_markdown,
                    "masked_markdown": masked_markdown,
                    "wrong_blocks": wrong_block_list
                }
            )

    for i in range(20):
        if i in count_statistic:
            print(count_statistic[i])

    return rebuild_train_data, rebuild_test_data