import cv2
import os
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import math 

def add_watermark(image_path, watermark_text, output_path=None, 
                 font_size=50, rotation_angle=45, opacity=128, 
                 color=(200, 200, 200), spacing=100):
    """
    给图片添加旋转水印
    
    参数:
    - image_path: 原始图片路径
    - watermark_text: 水印文字
    - output_path: 输出图片路径，默认为原文件名_watermark
    - font_size: 字体大小
    - rotation_angle: 旋转角度（度）
    - opacity: 透明度 (0-255)
    - color: 水印颜色 RGB元组
    - spacing: 水印间距
    """
    
    # 打开原始图片
    try:
        original_image = Image.open(image_path).convert('RGBA')
    except Exception as e:
        print(f"无法打开图片: {e}")
        return None
    
    # 获取图片尺寸
    width, height = original_image.size
    
    # 创建透明层用于绘制水印
    watermark_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(watermark_layer)

    font_paths = [
    # Linux中文字体
    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
    "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
    "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",
    "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc"
    ]

    for font_path in font_paths:
        try:
            font = ImageFont.truetype(font_path, font_size)
            print(f"成功加载字体: {font_path}")
            break
        except:
            continue
    
    # 获取文字尺寸
    text_bbox = draw.textbbox((0, 0), watermark_text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    # 计算旋转后的文字尺寸
    angle_rad = math.radians(rotation_angle)
    rotated_width = abs(text_width * math.cos(angle_rad)) + abs(text_height * math.sin(angle_rad))
    rotated_height = abs(text_width * math.sin(angle_rad)) + abs(text_height * math.cos(angle_rad))
    
    # 创建单个水印文字图像
    text_image = Image.new('RGBA', (int(rotated_width + 20), int(rotated_height + 20)), (0, 0, 0, 0))
    text_draw = ImageDraw.Draw(text_image)
    
    # 在文字图像中心绘制文字
    text_x = (text_image.width - text_width) // 2
    text_y = (text_image.height - text_height) // 2
    text_draw.text((text_x, text_y), watermark_text, font=font, fill=(*color, opacity))
    
    # 旋转文字图像
    rotated_text = text_image.rotate(rotation_angle, expand=True)
    
    # 计算平铺的行列数
    tile_width = rotated_text.width + spacing
    tile_height = rotated_text.height + spacing
    cols = (width // tile_width) + 2
    rows = (height // tile_height) + 2
    
    # 平铺水印
    for row in range(rows):
        for col in range(cols):
            x = col * tile_width - spacing
            y = row * tile_height - spacing
            
            # 错位排列，让水印更自然
            if row % 2 == 1:
                x += tile_width // 2
            
            if x < width and y < height:
                watermark_layer.paste(rotated_text, (x, y), rotated_text)
    
    # 合并原图和水印层
    watermarked_image = Image.alpha_composite(original_image, watermark_layer)
    
    # 转换为RGB格式（去除透明通道）
    final_image = Image.new('RGB', watermarked_image.size, (255, 255, 255))
    final_image.paste(watermarked_image, mask=watermarked_image.split()[-1])

    # 设置输出路径
    if output_path is None:
        name, ext = os.path.splitext(image_path)
        output_path = f"{name}_watermark{ext}"
    
    # 保存图片
    try:
        final_image.save(output_path, quality=95)
        print(f"水印添加成功！保存到: {output_path}")
        return output_path
    except Exception as e:
        print(f"保存图片失败: {e}")
        return None
    
def process_image_to_scan_style(image_path, output_processed_path, output_watermark_path, params):
    """
    将单张图片转换为扫描件风格。
    params 字典包含各种效果参数。
    """
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read image {image_path}")
        return

    # 1. 灰度化 (如果需要)
    if params.get('grayscale', True):
        img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        img_gray = img # 或者直接对彩色图进行后续处理

    # 2. 对比度增强和亮度调整
    alpha = params.get('contrast', 0.2) # 对比度因子
    beta = params.get('brightness', 30) # 亮度因子
    img_processed = cv2.convertScaleAbs(img_gray, alpha=alpha, beta=beta)

    # 3. 二值化 (常用，但不是必须的，取决于是否需要纯粹的文档感)
    if params.get('binarize', False):
        thresh_type = params.get('thresh_type', 'adaptive')
        if thresh_type == 'adaptive':
            img_processed = cv2.adaptiveThreshold(img_processed, 255,
                                                  cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                  cv2.THRESH_BINARY, 11, 2)
        else: # 全局阈值
            _, img_processed = cv2.threshold(img_processed, params.get('global_thresh', 127),
                                             255, cv2.THRESH_BINARY)

    # 4. 颜色倾向 (如果不是二值化)
    if not params.get('binarize', False) and params.get('color_tint', None):
        # 假设我们想加入泛黄效果
        # 需要将img_processed转回彩色，或者在灰度化之前进行
        # 这里为了简化，假设我们是在彩色图像上进行处理
        # 实际操作可能需要将原始彩色图传递到这里
        if 'yellow' in params['color_tint']:
            # 简单泛黄，增加R和G通道值，降低B通道值
            # 需要更复杂的颜色矩阵或LUT
            img_processed_color = cv2.cvtColor(img_processed, cv2.COLOR_GRAY2BGR) # 如果是灰度图转彩色
            rows, cols, _ = img_processed_color.shape
            lut = np.zeros((256, 1, 3), dtype=np.uint8)
            for i in range(256):
                lut[i][0][0] = min(255, i * 1.05)  # Blue
                lut[i][0][1] = min(255, i * 1.1)   # Green
                lut[i][0][2] = min(255, i * 1.15)  # Red
            img_processed_color = cv2.LUT(img_processed_color, lut)
            img_processed = img_processed_color


    # 5. 噪声添加 (在二值化后或灰度图上添加更明显)
    if params.get('add_noise', False):
        noise_type = params.get('noise_type', 'gaussian')
        noise_amount = params.get('noise_amount', 0.02) # 噪声强度
        if noise_type == 'gaussian':
            # 生成高斯噪声
            mean = 0
            var = noise_amount * 255 # 噪声方差
            sigma = var**0.5
            gaussian = np.random.normal(mean, sigma, img_processed.shape)
            noisy_image = img_processed + gaussian
            img_processed = np.clip(noisy_image, 0, 255).astype(np.uint8)
        elif noise_type == 'salt_pepper':
            s_vs_p = 0.5
            amount = noise_amount
            out = np.copy(img_processed)
            # Salt mode
            num_salt = np.ceil(amount * img_processed.size * s_vs_p)
            coords = [np.random.randint(0, i - 1, int(num_salt)) for i in img_processed.shape]
            out[tuple(coords)] = 255
            # Pepper mode
            num_pepper = np.ceil(amount * img_processed.size * (1. - s_vs_p))
            coords = [np.random.randint(0, i - 1, int(num_pepper)) for i in img_processed.shape]
            out[tuple(coords)] = 0
            img_processed = out
    # --- 新增锐化步骤 ---
    if params.get('add_sharpening', True): # 默认启用锐化
        # 应用一个简单的锐化核
        # 锐化核越大，锐化效果越强，但可能放大噪声
        kernel_sharpening = np.array([[-1,-1,-1],
                                      [-1, 9,-1],
                                      [-1,-1,-1]])
        # 另一种更柔和的锐化核
        # kernel_sharpening = np.array([[0,-1,0],
        #                               [-1,5,-1],
        #                               [0,-1,0]])
        img_processed = cv2.filter2D(img_processed, -1, kernel_sharpening)

    # 6. 纹理叠加
    if params.get('add_texture', False) and params.get('texture_path'):
        texture_img = cv2.imread(params['texture_path'], cv2.IMREAD_GRAYSCALE)
        if texture_img is not None:
            # 缩放纹理以匹配图像大小
            texture_img_resized = cv2.resize(texture_img, (img_processed.shape[1], img_processed.shape[0]))
            # 混合模式 (这里使用简单的加权叠加，可以尝试更多复杂的混合模式)
            alpha_blend = params.get('texture_blend_alpha', 0.1) # 纹理透明度
            img_processed = cv2.addWeighted(img_processed, 1 - alpha_blend, texture_img_resized, alpha_blend, 0)
        else:
            print(f"Warning: Could not load texture image {params['texture_path']}")

    # 7. 几何变换 (随机轻微旋转、透视)
    if params.get('random_rotate', False):
        angle = np.random.uniform(-params.get('max_rotate_angle', 1), params.get('max_rotate_angle', 1))
        rows, cols = img_processed.shape[:2]
        M = cv2.getRotationMatrix2D(((cols-1)/2.0,(rows-1)/2.0), angle, 1)
        img_processed = cv2.warpAffine(img_processed, M, (cols,rows), borderValue=(255,255,255) if img_processed.ndim == 2 else (255,255,255)) # 边框填充白色
    # 轻微扭曲
    if params.get('wrapp', False):
        amplitude = params.get("amplitude", 3)
        frequency = params.get("frequency", 0.01)
        h, w = img_processed.shape[:2]
        print(h, w)
        # 创建映射表
        # map_x 和 map_y 存储目标图像中每个像素点对应到原始图像的 (x, y) 坐标
        map_x = np.zeros((h, w), dtype=np.float32)
        map_y = np.zeros((h, w), dtype=np.float32)

        for y in range(h):
            for x in range(w):
                # 原始x坐标不变
                map_x[y, x] = x
                # 原始y坐标根据x坐标进行正弦波偏移
                # 这里的目的是让垂直方向的像素点根据其水平位置进行上下移动
                map_y[y, x] = y + amplitude * np.sin(x * frequency)

        # 应用重映射
        # cv2.INTER_LINEAR 是线性插值，通常效果不错
        # cv2.BORDER_CONSTANT 填充超出边界的像素，并使用 borderValue
        img_processed = cv2.remap(img_processed, map_x, map_y, cv2.INTER_LINEAR,
                                borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0)) # 黑色填充

    cv2.imwrite(output_processed_path, img_processed)
    print(f"Processed {image_path} -> {output_processed_path}")
    
    # 添加水印
    if params.get('watermark', False):
        watermark_text = params.get("watermark_text", "wps_zr")
        add_watermark(output_processed_path, watermark_text=watermark_text, output_path=output_watermark_path)


def batch_process_images(input_dir, output_dir, output_watermark, params, num_workers=8):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    image_files = [f for f in os.listdir(input_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]

    # 可以使用多线程或多进程加速
    # from concurrent.futures import ThreadPoolExecutor
    # with ThreadPoolExecutor(max_workers=num_workers) as executor:
    #     for image_file in image_files:
    #         input_path = os.path.join(input_dir, image_file)
    #         output_path = os.path.join(output_dir, image_file)
    #         executor.submit(process_image_to_scan_style, input_path, output_path, params)

    for image_file in image_files:
        input_path = os.path.join(input_dir, image_file)
        output_path = os.path.join(output_dir, image_file)
        output_watermark_path = os.path.join(output_watermark, image_file)
        process_image_to_scan_style(input_path, output_path, output_watermark_path, params)

if __name__ == "__main__":
    # 示例参数
    scan_params = {
        'grayscale': True,
        'contrast': 0.2,
        'brightness': 80,
        'binarize': True,
        'thresh_type': 'adaptive',
        'add_noise': False,
        'noise_type': 'gaussian',
        'noise_amount': 0.01,
        "add_sharpening": True, 
        # 'add_texture': True,
        # 'texture_path': 'path/to/your/paper_texture.jpg', # 确保提供一个纸张纹理图片
        # 'texture_blend_alpha': 0.15,
        'random_rotate': True,
        'max_rotate_angle': 5, # 最大旋转角度（度）
        'wrapp': True,
        "amplitude": 3,
        "frequency": 0.01,
        'watermark' : True
    }

    # 调用示例 (请替换为您的实际路径)
    input_folder = '/home/<USER>/zhangran/Open_Extract/synthetic_data/img_process/pre'
    output_folder = '/home/<USER>/zhangran/Open_Extract/synthetic_data/img_process/after'
    output_watermark = "/home/<USER>/zhangran/Open_Extract/synthetic_data/img_process/watermark"
    batch_process_images(input_folder, output_folder, output_watermark, scan_params)