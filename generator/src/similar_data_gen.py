from prompts.topic_gen import topic_gen_prompt
from utils.parse_json import get_json_object
import json
from prompts.similar_gen import sim_prompt
from openai import OpenAI
import concurrent.futures
from tqdm import tqdm  # 可选进度条

model = "Qwen3-14B"
api_key = "c8f8a40c-59aa-4f75-b5c1-b8541b6c0338"
base_url = "http://kmd-api.kas.wps.cn/api/11334-v3/kQUONi/v1"

client = OpenAI(api_key=api_key, base_url=base_url)


def get_completion(client, prompt, model, seed=1):
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt}
        ],
        temperature=0.99,
        seed=seed
        
    )
    response = completion.choices[0].message.content
    
    return response

def get_completion_streaming(client, prompt, model="Qwen/Qwen2.5-72B-Instruct"):
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt},
        ],
        stream=True,
    )
    response = ""
    for chunk in completion:
        answer_chunk = chunk.choices[0].delta.content

        if answer_chunk != '':
            response += answer_chunk
    return response

def filter_no_checkbox(json_object):
    d = json.dumps(json_object, ensure_ascii=False)
    return "options" not in d
        
def generate_sim_doc(json_object):
    d = json.dumps(json_object, ensure_ascii=False)
    content = sim_prompt.format(raw_data=d)
    response = get_completion(client, content, model=model)
    try:
        response = get_json_object(response)
        response = json.loads(response)
    except Exception as e:
        print("Error parsing JSON:", response, e)
        return None
    return response

from datetime import date
import os

if __name__ == "__main__":
    similar_data_json = "/home/<USER>/kas_cache/pengying/llm_checkbox_gen/data/customized/kailaiying.json"
    data_dir = "/home/<USER>/kas_cache/pengying/llm_checkbox_gen/data/customized"

    # 每次生成2000个样本
    all_document_count = 0
    document_list = []
    dataset_name = "similar_data"

    with open(similar_data_json, "r", encoding="utf-8") as f:
        data_sample_list = json.load(f)

    current_date = date.today()
    
    data_gen_dir = os.path.join(data_dir, f"{current_date}_{dataset_name}")
    os.makedirs(data_gen_dir, exist_ok=True)

    # 每一页都生成50次数据，n * 50
    for i in range(50):
        tasks = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
            for data_sample in data_sample_list:
                if filter_no_checkbox(data_sample):
                    print("sample has no options, checkbox, skip")
                    continue
                    
                print("=====================Start to gen data=====================")

                future = executor.submit(
                    generate_sim_doc,
                    data_sample
                )
                tasks.append(future)

            for future in tqdm(concurrent.futures.as_completed(tasks), total=len(tasks)):
                result = future.result()
                if result:
                    document_list.append(result)
                    file_path = os.path.join(data_dir, f"{current_date}_{dataset_name}", "llm_gen_data.json")
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(document_list, f, indent=2, ensure_ascii=False)
                
    print("Total documents generated:", len(document_list))
