from prompts.topic_gen import topic_gen_prompt
from utils.parse_json import get_json_object
import json
from prompts.table_gen import json_schema_for_table_gen, table_gen_prompt, table_gen_example
from openai import OpenAI
import concurrent.futures
from tqdm import tqdm  # 可选进度条

model = "Qwen3-14B"
api_key = "c8f8a40c-59aa-4f75-b5c1-b8541b6c0338"
base_url = "http://kmd-api.kas.wps.cn/api/11334-v3/kQUONi/v1"

client = OpenAI(api_key=api_key, base_url=base_url)


def get_completion(client, prompt, model):
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt}
        ],
    )
    response = completion.choices[0].message.content
    
    return response

def get_completion_streaming(client, prompt, model="Qwen/Qwen2.5-72B-Instruct"):
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt},
        ],
        stream=True,
    )
    response = ""
    for chunk in completion:
        answer_chunk = chunk.choices[0].delta.content

        if answer_chunk != '':
            response += answer_chunk
    return response

def generate_document(industry, topic, client, document_gen_prompt, json_schema_for_doc_gen, example):
    prompt = document_gen_prompt.format(
        industry=industry,
        topic=topic,
        example=example,
        schema=json.dumps(json_schema_for_doc_gen, ensure_ascii=False)
    )
    response = get_completion(client, prompt, model=model)
    
    try:
        doc_obj = get_json_object(response)
        doc_json = json.loads(doc_obj)
        doc_json['industry'] = industry
        return doc_json
    except Exception as e:
        print("Error parsing JSON:", response, e)
        return None

def generate_table(industry, topic, client, table_gen_prompt, json_schema_for_table_gen, table_gen_example):
    prompt = table_gen_prompt.format(
        industry=industry,
        topic=topic,
        example=table_gen_example,
        schema=json.dumps(json_schema_for_table_gen, ensure_ascii=False)
    )
    response = get_completion(client, prompt, model=model)
    
    try:
        doc_obj = get_json_object(response)
        doc_json = json.loads(doc_obj)
        doc_json['industry'] = industry
        return doc_json
    except Exception as e:
        print("Error parsing JSON:", response, e)
        return None

from datetime import date
import os

if __name__ == "__main__":
    data_dir = "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data/llm_train_data"

    # 每次生成2000个样本
    all_document_count = 0
    max_samples = 2
    split_samples = 2
    current_date = date.today()

    document_list = []
    
    
    for i in range(100000000):
        current_sample_stage = (all_document_count // split_samples + 1) * split_samples
         
        data_gen_dir = os.path.join(data_dir, f"{current_date}_{current_sample_stage}")
        os.makedirs(data_gen_dir, exist_ok=True)
        
        if all_document_count > max_samples:
            break
    
        # 第一步：生成行业和对应的topic和理由
        industry_list = get_completion(client, topic_gen_prompt, model=model)
        industry_topic = get_json_object(industry_list)
        industry_topic_list = json.loads(industry_topic)

        tasks = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            for industry_topic_object in industry_topic_list:
                industry = industry_topic_object["industry"]
                topics = industry_topic_object["topics"]
                for topic in topics:
                    future = executor.submit(
                        generate_table,
                        industry, topic, client,
                        table_gen_prompt, json_schema_for_table_gen, table_gen_example
                    )
                    tasks.append(future)

            # 收集结果
            for future in tqdm(concurrent.futures.as_completed(tasks), total=len(tasks)):
                result = future.result()
                if result:
                    if len(document_list) >= split_samples:
                        file_path = os.path.join(data_dir, f"{current_date}_{current_sample_stage}", "llm_gen_data.json")
                        with open(file_path, "w", encoding="utf-8") as f:
                            json.dump(document_list, f, ensure_ascii=False, indent=4)
                        document_list = []
                        break

                    document_list.append(result)
                    all_document_count += 1            
            if len(document_list) >= split_samples:
                file_path = os.path.join(data_dir, f"{current_date}_{current_sample_stage}", "llm_gen_data.json")
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(document_list, f, ensure_ascii=False, indent=4)
                document_list = []

    print("Total documents generated:", len(document_list))
