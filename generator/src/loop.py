from image_gen import gen_image_data, gen_complex_image_data
from llm_train_data_gen import gen_short_long_data
from datetime import date
import os
import time

data_path = "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data/llm_train_data"

read_list = [
    "2025-06-19_2000",
    "2025-06-19_4000",
    "2025-06-19_6000",
    "2025-06-19_8000",
    "2025-06-19_10000",
    "2025-06-19_12000",
    "2025-06-19_14000"
    ]
sleep_second = 60 
complexd = True

for i in range(10000000000):
    current_date = date.today()

    data_dir_list = os.listdir(data_path)

    for data_dir in data_dir_list:
        file_path = os.path.join(data_path, data_dir)
        print()
        if data_dir not in read_list and not os.path.exists(os.path.join(data_dir, "images")) and os.path.exists(os.path.join(file_path, "llm_gen_data.json")):
            # 生成图片数据
            print("开始生成图片")
            if complexd:
                gen_complex_image_data(file_path)
            else:
                gen_image_data(file_path)
            print("开始生成大模型训练数据")
            # 生成大模型训练数据
            
            gen_short_long_data(file_path, complexd)

            print("生成结束，sleep 1 hour")
            read_list.append(data_dir)

            time.sleep(60 * 60 * 1)
            

    print(f"sleep {sleep_second} seconds")
    time.sleep(sleep_second)


    


    