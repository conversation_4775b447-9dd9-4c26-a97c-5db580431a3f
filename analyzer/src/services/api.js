import axios from 'axios';

// 基础API配置 - 使用统一后端服务
const API_BASE_URL = 'http://localhost:8000';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  // 添加CORS相关配置
  headers: {
    'Content-Type': 'application/json',
  },
  // 允许跨域请求
  withCredentials: false,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.baseURL}${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }
    return Promise.reject(error);
  }
);

/**
 * 获取数据集列表
 */
export const getDatasetList = async () => {
  try {
    const response = await api.get('/api/datasets');
    // 新的API返回JSON格式的数据集列表
    return response.data.map(dataset => dataset.name);
  } catch (error) {
    console.error('Failed to fetch dataset list:', error);
    throw error;
  }
};

/**
 * 获取指定数据集的解析结果
 */
export const getParseResults = async (datasetName) => {
  try {
    // 使用新的API获取解析结果
    const response = await api.get(`/api/datasets/${datasetName}/parse_results`);
    console.log('Successfully loaded parse results:', response.data);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch parse results for ${datasetName}:`, error);
    throw error;
  }
};

/**
 * 获取指定数据集的图片列表
 */
export const getImageList = async (datasetName) => {
  try {
    const response = await api.get(`/api/datasets/${datasetName}/images`);
    // 新的API返回JSON格式的图片列表
    return response.data.map(image => image.filename);
  } catch (error) {
    console.error(`Failed to fetch image list for ${datasetName}:`, error);
    throw error;
  }
};

/**
 * 获取图片URL
 */
export const getImageUrl = (datasetName, imageName) => {
  return `${API_BASE_URL}/static/dataset/${datasetName}/images/${imageName}`;
};

// ==================== 标注功能API ====================

/**
 * 获取标注列表
 */
export const getAnnotations = async (params = {}) => {
  try {
    const response = await api.get('/api/annotations', { params });
    return response.data;
  } catch (error) {
    console.error('Failed to fetch annotations:', error);
    throw error;
  }
};

/**
 * 创建标注
 */
export const createAnnotation = async (annotationData) => {
  try {
    const response = await api.post('/api/annotations', annotationData);
    return response.data;
  } catch (error) {
    console.error('Failed to create annotation:', error);
    throw error;
  }
};

/**
 * 获取单个标注
 */
export const getAnnotation = async (annotationId) => {
  try {
    const response = await api.get(`/api/annotations/${annotationId}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch annotation ${annotationId}:`, error);
    throw error;
  }
};

/**
 * 更新标注
 */
export const updateAnnotation = async (annotationId, updateData) => {
  try {
    const response = await api.put(`/api/annotations/${annotationId}`, updateData);
    return response.data;
  } catch (error) {
    console.error(`Failed to update annotation ${annotationId}:`, error);
    throw error;
  }
};

/**
 * 删除标注
 */
export const deleteAnnotation = async (annotationId) => {
  try {
    const response = await api.delete(`/api/annotations/${annotationId}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to delete annotation ${annotationId}:`, error);
    throw error;
  }
};

/**
 * 获取图片的所有标注
 */
export const getImageAnnotations = async (imageId) => {
  try {
    const response = await api.get(`/api/images/${imageId}/annotations`);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch annotations for image ${imageId}:`, error);
    throw error;
  }
};

/**
 * 通过数据集名称和文件名查找图片ID
 */
export const findImageId = async (datasetName, filename) => {
  try {
    const response = await api.get(`/api/datasets/${datasetName}/images`);
    const images = response.data;
    const image = images.find(img => img.filename === filename);
    return image ? image.id : null;
  } catch (error) {
    console.error(`Failed to find image ID for ${filename}:`, error);
    throw error;
  }
};

/**
 * 获取数据集的标注列表
 */
export const getDatasetAnnotations = async (datasetName) => {
  try {
    const response = await api.get('/api/annotations', {
      params: { dataset_name: datasetName }
    });
    return response.data;
  } catch (error) {
    console.error('获取数据集标注失败:', error);
    throw error;
  }
};

/**
 * 批量创建标注
 */
export const batchCreateAnnotations = async (batchData) => {
  try {
    const response = await api.post('/api/annotations/batch', batchData);
    return response.data;
  } catch (error) {
    console.error('批量创建标注失败:', error);
    throw error;
  }
};

/**
 * 自动生成标注
 */
export const autoGenerateAnnotations = async (datasetName, options = {}) => {
  try {
    const params = new URLSearchParams();
    if (options.annotator) params.append('annotator', options.annotator);
    if (options.overwrite !== undefined) params.append('overwrite', options.overwrite);

    const url = `/api/annotations/auto-generate/${datasetName}${params.toString() ? '?' + params.toString() : ''}`;
    const response = await api.post(url);
    return response.data;
  } catch (error) {
    console.error('自动生成标注失败:', error);
    throw error;
  }
};

// ==================== 人工标注数据API ====================
// 注意：不再使用llm_gen_data.json文件，该文件是用于LLM训练的JSON Schema定义
// 准确率计算应该基于/api/annotations接口返回的人工标注数据

/**
 * 根据图片文件名查找对应的人工标注数据
 * @param {string} datasetName - 数据集名称
 * @param {string} filename - 图片文件名
 * @returns {Object|null} 匹配的标注数据
 */
export const findAnnotationDataByFilename = async (datasetName, filename) => {
  try {
    // 获取数据集的所有标注数据
    const annotations = await getDatasetAnnotations(datasetName);

    if (!annotations || !Array.isArray(annotations)) {
      console.warn(`数据集 ${datasetName} 没有标注数据`);
      return null;
    }

    // 查找匹配的标注
    const matchingAnnotation = annotations.find(annotation =>
      annotation.image_filename === filename
    );

    if (matchingAnnotation) {
      console.log(`找到图片 ${filename} 的标注数据:`, matchingAnnotation);
      return matchingAnnotation;
    } else {
      console.warn(`未找到图片 ${filename} 的标注数据`);
      return null;
    }
  } catch (error) {
    console.error(`查找图片 ${filename} 的标注数据失败:`, error);
    return null;
  }
};

/**
 * 获取VL LLM结果
 */
export const getVLLLMResults = async (datasetName) => {
  try {
    const response = await api.get(`/vl_llm_results/${datasetName}/`);
    const html = response.data;
    const files = parseDirectoryListing(html);
    const vlLLMFiles = files.filter(name => name.endsWith('_vl_llm.json'));
    
    const results = {};
    for (const fileName of vlLLMFiles) {
      try {
        const fileResponse = await api.get(`/vl_llm_results/${datasetName}/${fileName}`);
        const baseName = fileName.replace('_vl_llm.json', '');
        results[baseName] = fileResponse.data;
      } catch (error) {
        console.warn(`Failed to load VL LLM result file ${fileName}:`, error);
      }
    }
    
    return results;
  } catch (error) {
    console.error(`Failed to fetch VL LLM results for ${datasetName}:`, error);
    return {};
  }
};

/**
 * 获取MonkeyOCR结果
 */
export const getMonkeyOCRResults = async (datasetName, imageName) => {
  try {
    const baseName = imageName.replace(/\.(png|jpg|jpeg|gif|webp)$/i, '');
    const response = await api.get(`/dataset/${datasetName}/monkey_ocr/${baseName}/`);
    const html = response.data;
    const files = parseDirectoryListing(html);
    
    const results = {};
    for (const fileName of files) {
      if (fileName.endsWith('.json')) {
        try {
          const fileResponse = await api.get(`/dataset/${datasetName}/monkey_ocr/${baseName}/${fileName}`);
          results[fileName] = fileResponse.data;
        } catch (error) {
          console.warn(`Failed to load MonkeyOCR result file ${fileName}:`, error);
        }
      }
    }
    
    return results;
  } catch (error) {
    console.error(`Failed to fetch MonkeyOCR results for ${datasetName}/${imageName}:`, error);
    return {};
  }
};

/**
 * 解析目录列表HTML
 */
const parseDirectoryListing = (html) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  const links = doc.querySelectorAll('a');
  
  const items = [];
  links.forEach(link => {
    const href = link.getAttribute('href');
    if (href && href !== '../' && !href.startsWith('?')) {
      // 移除末尾的斜杠（如果是目录）
      let name = href.endsWith('/') ? href.slice(0, -1) : href;
      if (name) {
        // 对URL编码的文件名进行解码
        try {
          name = decodeURIComponent(name);
        } catch (e) {
          console.warn('Failed to decode filename:', name, e);
          // 如果解码失败，使用原始名称
        }
        items.push(name);
      }
    }
  });
  
  return items;
};

/**
 * 检查文件是否存在
 */
export const checkFileExists = async (path) => {
  try {
    await api.head(path);
    return true;
  } catch (error) {
    return false;
  }
};



export default api;
