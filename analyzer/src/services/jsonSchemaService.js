/**
 * JSON Schema数据处理服务
 * 专门处理JSON Schema格式的表格数据
 */

import { getJsonSchemaTrainData, findJsonSchemaDataByFilename } from './api';
import { JsonSchemaTableParser } from '../utils/dataProcessor';

class JsonSchemaService {
  constructor() {
    this.cachedData = null;
    this.lastFetchTime = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 获取JSON Schema训练数据（带缓存）
   */
  async getTrainData() {
    const now = Date.now();
    
    // 检查缓存是否有效
    if (this.cachedData && this.lastFetchTime && 
        (now - this.lastFetchTime) < this.cacheTimeout) {
      return this.cachedData;
    }

    try {
      console.log('获取JSON Schema训练数据...');
      const data = await getJsonSchemaTrainData();
      
      if (data) {
        this.cachedData = data;
        this.lastFetchTime = now;
        console.log('JSON Schema数据获取成功，数据量:', Array.isArray(data) ? data.length : 'unknown');
      } else {
        console.warn('未获取到JSON Schema数据，将使用传统模式');
      }
      
      return data;
    } catch (error) {
      console.error('获取JSON Schema数据失败:', error);
      return null;
    }
  }

  /**
   * 根据案例数据查找对应的JSON Schema数据
   */
  async findSchemaDataForCase(caseData) {
    if (!caseData || !caseData.fileName) {
      return null;
    }

    const trainData = await this.getTrainData();
    if (!trainData) {
      return null;
    }

    return findJsonSchemaDataByFilename(trainData, caseData.fileName);
  }

  /**
   * 解析JSON Schema数据为标准格式
   */
  parseSchemaData(schemaData) {
    if (!schemaData) {
      return null;
    }

    try {
      return JsonSchemaTableParser.parseJsonSchemaTable(schemaData);
    } catch (error) {
      console.error('解析JSON Schema数据失败:', error);
      return null;
    }
  }

  /**
   * 检查是否有可用的JSON Schema数据
   */
  async hasSchemaData() {
    const data = await this.getTrainData();
    return data && Array.isArray(data) && data.length > 0;
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cachedData = null;
    this.lastFetchTime = null;
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus() {
    return {
      hasCachedData: !!this.cachedData,
      lastFetchTime: this.lastFetchTime,
      cacheAge: this.lastFetchTime ? Date.now() - this.lastFetchTime : null,
      isExpired: this.lastFetchTime ? 
        (Date.now() - this.lastFetchTime) > this.cacheTimeout : true
    };
  }

  /**
   * 验证JSON Schema数据格式
   */
  validateSchemaData(data) {
    if (!data || typeof data !== 'object') {
      return { valid: false, error: '数据不是有效的对象' };
    }

    if (!data.elements || !Array.isArray(data.elements)) {
      return { valid: false, error: '缺少elements数组' };
    }

    const tableElement = data.elements.find(el => el.type === 'table');
    if (!tableElement) {
      return { valid: false, error: '未找到table类型的元素' };
    }

    if (!tableElement.rows || !Array.isArray(tableElement.rows)) {
      return { valid: false, error: '表格缺少rows数组' };
    }

    return { valid: true };
  }

  /**
   * 获取数据统计信息
   */
  async getDataStats() {
    const data = await this.getTrainData();
    if (!data || !Array.isArray(data)) {
      return null;
    }

    const stats = {
      totalItems: data.length,
      validTables: 0,
      complexTables: 0,
      totalCells: 0,
      checkboxCells: 0,
      listCells: 0
    };

    data.forEach(item => {
      const validation = this.validateSchemaData(item);
      if (validation.valid) {
        stats.validTables++;
        
        const tableElement = item.elements.find(el => el.type === 'table');
        if (tableElement && tableElement.rows) {
          let hasComplexContent = false;
          
          tableElement.rows.forEach(row => {
            if (Array.isArray(row)) {
              row.forEach(cell => {
                stats.totalCells++;
                
                if (typeof cell.content === 'object') {
                  hasComplexContent = true;
                  if (cell.content.type === 'checkbox') {
                    stats.checkboxCells++;
                  } else if (cell.content.type === 'list') {
                    stats.listCells++;
                  }
                }
              });
            }
          });
          
          if (hasComplexContent) {
            stats.complexTables++;
          }
        }
      }
    });

    return stats;
  }
}

// 创建单例实例
const jsonSchemaService = new JsonSchemaService();

export default jsonSchemaService;
export { JsonSchemaService };
