/**
 * 数据处理工具函数
 * 支持JSON Schema格式的表格数据处理
 */

/**
 * JSON Schema表格数据解析器
 */
export class JsonSchemaTableParser {
  /**
   * 解析JSON Schema格式的表格数据
   * @param {Object} jsonData - JSON Schema格式的数据
   * @returns {Object} 解析后的表格数据
   */
  static parseJsonSchemaTable(jsonData) {
    if (!jsonData || !jsonData.elements || !Array.isArray(jsonData.elements)) {
      return null;
    }

    // 查找表格元素
    const tableElement = jsonData.elements.find(element => element.type === 'table');
    if (!tableElement || !tableElement.rows) {
      return null;
    }

    return {
      type: 'json_schema',
      rows: tableElement.rows,
      structure: this.analyzeTableStructure(tableElement.rows),
      content: this.extractTableContent(tableElement.rows)
    };
  }

  /**
   * 分析表格结构
   * @param {Array} rows - 表格行数据
   * @returns {Object} 表格结构信息
   */
  static analyzeTableStructure(rows) {
    if (!rows || rows.length === 0) {
      return { rows: 0, cols: 0, mergedCells: [], complexCells: [] };
    }

    let maxCols = 0;
    const mergedCells = [];
    const complexCells = [];

    rows.forEach((row, rowIndex) => {
      if (Array.isArray(row)) {
        let colCount = 0;
        row.forEach((cell, colIndex) => {
          const colspan = cell.colspan || 1;
          const rowspan = cell.rowspan || 1;
          colCount += colspan;

          // 记录合并单元格
          if (colspan > 1 || rowspan > 1) {
            mergedCells.push({
              row: rowIndex,
              col: colIndex,
              colspan,
              rowspan
            });
          }

          // 记录复杂内容单元格
          if (typeof cell.content === 'object') {
            complexCells.push({
              row: rowIndex,
              col: colIndex,
              type: cell.content.type || 'unknown',
              content: cell.content
            });
          }
        });
        maxCols = Math.max(maxCols, colCount);
      }
    });

    return {
      rows: rows.length,
      cols: maxCols,
      mergedCells,
      complexCells
    };
  }

  /**
   * 提取表格内容为二维数组
   * @param {Array} rows - 表格行数据
   * @returns {Array} 二维数组格式的表格内容
   */
  static extractTableContent(rows) {
    if (!rows || rows.length === 0) {
      return [];
    }

    return rows.map(row => {
      if (!Array.isArray(row)) return [];

      return row.map(cell => {
        return this.extractCellContent(cell.content);
      });
    });
  }

  /**
   * 提取单元格内容
   * @param {*} content - 单元格内容
   * @returns {string} 提取的文本内容
   */
  static extractCellContent(content) {
    if (typeof content === 'string') {
      return content;
    }

    if (typeof content === 'object' && content !== null) {
      switch (content.type) {
        case 'checkbox':
          return this.extractCheckboxContent(content);
        case 'list':
          return this.extractListContent(content);
        default:
          return JSON.stringify(content);
      }
    }

    return String(content || '');
  }

  /**
   * 提取复选框内容
   * @param {Object} checkboxData - 复选框数据
   * @returns {string} 格式化的复选框内容
   */
  static extractCheckboxContent(checkboxData) {
    if (!checkboxData.options || !Array.isArray(checkboxData.options)) {
      return '';
    }

    const checkedItems = checkboxData.options
      .filter(option => option.checked)
      .map(option => option.option);

    const uncheckedItems = checkboxData.options
      .filter(option => !option.checked)
      .map(option => option.option);

    // 返回格式化的字符串，便于比较
    return `${checkboxData.key || ''}:checked[${checkedItems.join(',')}];unchecked[${uncheckedItems.join(',')}]`;
  }

  /**
   * 提取列表内容
   * @param {Object} listData - 列表数据
   * @returns {string} 格式化的列表内容
   */
  static extractListContent(listData) {
    if (!listData.items || !Array.isArray(listData.items)) {
      return '';
    }

    return listData.items.join(';');
  }
}

/**
 * 将Markdown表格转换为HTML
 */
export const convertMarkdownTableToHTML = (markdownText) => {
  if (!markdownText || !markdownText.trim()) {
    return "<div style='color:gray;'>无表格内容</div>";
  }

  try {
    const lines = markdownText.trim().split('\n');
    const tableLines = [];
    let inTable = false;

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('|') && trimmedLine.endsWith('|')) {
        // 跳过分隔行（如 | --- | --- |）
        if (!trimmedLine.replace(/\|/g, '').replace(/-/g, '').replace(/ /g, '')) {
          continue;
        }
        tableLines.push(trimmedLine);
        inTable = true;
      } else if (inTable) {
        // 如果已经在表格中但当前行不是表格行，表格结束
        break;
      }
    }

    if (!tableLines.length) {
      return "<div style='color:gray;'>无有效表格内容</div>";
    }

    // 解析所有行的单元格，找出最大列数
    const allRows = [];
    let maxCols = 0;

    for (const line of tableLines) {
      // 移除首尾的|，然后按|分割
      const cells = line.slice(1, -1).split('|').map(cell => cell.trim());
      allRows.push(cells);
      maxCols = Math.max(maxCols, cells.length);
    }

    // 补齐所有行到相同列数
    allRows.forEach(row => {
      while (row.length < maxCols) {
        row.push('');
      }
    });

    // 构建HTML表格
    const html = ["<table border='1' style='border-collapse: collapse; width: 100%;'>"];

    allRows.forEach((cells, i) => {
      if (i === 0) {
        // 第一行作为表头
        html.push("<thead><tr>");
        cells.forEach(cell => {
          html.push(`<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>${cell}</th>`);
        });
        html.push("</tr></thead><tbody>");
      } else {
        // 其他行作为数据行
        html.push("<tr>");
        cells.forEach(cell => {
          html.push(`<td style='border: 1px solid #ddd; padding: 8px;'>${cell}</td>`);
        });
        html.push("</tr>");
      }
    });

    html.push("</tbody></table>");
    return html.join('');
  } catch (error) {
    return `<div style='color:red;'>表格解析失败: ${error.message}</div>`;
  }
};

/**
 * 提取文件名称（不含扩展名）- 支持图片和PDF文件
 */
export const getImageBaseName = (fileName) => {
  return fileName.replace(/\.(png|jpg|jpeg|gif|webp|pdf)$/i, '');
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化时间戳
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  
  // 如果是字符串格式的时间戳（如 "2025_06_20_16_54_56"）
  if (typeof timestamp === 'string' && timestamp.includes('_')) {
    const parts = timestamp.split('_');
    if (parts.length >= 6) {
      const [year, month, day, hour, minute, second] = parts;
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
  }
  
  // 如果是数字时间戳
  if (typeof timestamp === 'number') {
    return new Date(timestamp * 1000).toLocaleString();
  }
  
  return timestamp;
};

/**
 * 计算准确率（支持JSON Schema数据）
 */
export const calculateAccuracy = (expected, actual) => {
  if (!expected || !actual) return 0;

  // 检查是否为解析失败的结果
  const actualStr = String(actual);
  if (actualStr.includes('MonkeyOCR处理失败') ||
      actualStr.includes('MonkeyOCR文件上传失败') ||
      actualStr.includes('MonkeyOCR处理超时') ||
      actualStr.includes('MonkeyOCR获取结果失败') ||
      actualStr.includes('MonkeyOCR处理请求提交失败') ||
      actualStr.includes('VL LLM处理失败') ||
      actualStr.includes('KDC处理失败')) {
    return 0;
  }

  // 检查是否为JSON Schema格式的数据
  if (typeof expected === 'object' && expected.type === 'json_schema') {
    return calculateJsonSchemaAccuracy(expected, actual);
  }

  try {
    // 预处理：检测并处理重复内容
    const cleanedActual = preprocessContent(actualStr);

    // 尝试解析表格内容进行比较
    const expectedTable = parseTableContent(String(expected));
    const actualTable = parseTableContent(cleanedActual);

    console.log('准确率计算调试:', {
      hasExpectedTable: !!expectedTable,
      hasActualTable: !!actualTable,
      expectedRows: expectedTable ? expectedTable.length : 0,
      actualRows: actualTable ? actualTable.length : 0,
      originalContentLength: actualStr.length,
      cleanedContentLength: cleanedActual.length,
      hasMultipleTables: actualStr.split('<table').length > 2,
      wasContentCleaned: actualStr !== cleanedActual,
      expectedSample: expectedTable ? expectedTable.slice(0, 3) : null,
      actualSample: actualTable ? actualTable.slice(0, 3) : null
    });

    if (expectedTable && actualTable) {
      const accuracy = compareTableData(expectedTable, actualTable);
      console.log('表格比较准确率:', accuracy);
      return accuracy;
    } else {
      // 如果无法解析为表格，使用简单字符串比较
      const accuracy = calculateAccuracySimple(expected, cleanedActual);
      console.log('简单字符串比较准确率:', accuracy);
      return accuracy;
    }
  } catch (error) {
    console.warn('表格解析失败，使用简单算法:', error);
    return calculateAccuracySimple(expected, actual);
  }
};

/**
 * 预处理内容，移除重复的表格或文本
 */
const preprocessContent = (content) => {
  if (!content || typeof content !== 'string') return content;

  // 处理HTML表格重复
  if (content.includes('<table')) {
    const tableMatches = content.match(/<table[^>]*>.*?<\/table>/gs);
    if (tableMatches && tableMatches.length > 1) {
      const firstTable = tableMatches[0];
      const firstTableNormalized = normalizeTableContent(firstTable);

      // 检查是否所有表格都是重复的
      const allDuplicate = tableMatches.slice(1).every(table =>
        normalizeTableContent(table) === firstTableNormalized
      );

      if (allDuplicate) {
        // 如果所有表格都是重复的，只保留第一个
        return firstTable;
      }
    }
  }

  // 处理其他类型的重复内容（如重复的文本块）
  // 这里可以根据需要添加更多的重复检测逻辑

  return content;
};

/**
 * 统一的准确率计算工具函数
 * 确保所有组件使用相同的准确率计算逻辑
 * 支持JSON Schema数据作为基准
 */
export const calculateAccuracyForCase = (caseData, annotationData, useAnnotationAsBaseline = false, jsonSchemaData = null) => {
  // 获取基准数据（优先使用JSON Schema数据，然后是人工标注）
  const getBaselineData = () => {
    // 优先使用JSON Schema数据作为基准
    if (jsonSchemaData) {
      const parsedJsonSchema = JsonSchemaTableParser.parseJsonSchemaTable(jsonSchemaData);
      console.log('====== parsedJsonSchema: ', JSON.stringify(parsedJsonSchema));
      if (parsedJsonSchema) {
        return parsedJsonSchema;
      }
    }

    // 其次使用人工标注数据
    if (useAnnotationAsBaseline && annotationData?.table_content) {
      // 尝试解析为JSON Schema格式（不依赖annotation_type字段）
      try {
        const jsonData = JSON.parse(annotationData.table_content);

        // 检查是否为JSON Schema格式的数据
        if (jsonData && typeof jsonData === 'object' && jsonData.elements) {
          const parsedJsonSchema = JsonSchemaTableParser.parseJsonSchemaTable(jsonData);
          if (parsedJsonSchema) {
            console.log('成功解析JSON Schema标注数据');
            return parsedJsonSchema;
          }
        }
      } catch (error) {
        console.debug('标注数据不是JSON格式，使用原有逻辑:', error.message);
      }

      // 如果不是JSON Schema格式，使用原有逻辑
      return annotationData.table_content;
    }

    // 最后使用KDC Plain作为基准
    return caseData.kdcPlain?.result?.data?.[0]?.plain || '';
  };

  const baselineData = getBaselineData();

  if (!baselineData) {
    console.warn('无法获取基准数据进行准确率计算');
    return {
      baselineData: null,
      baselineText: '',
      accuracies: {}
    };
  }

  // 兼容性处理：如果baselineData是字符串，保持原有逻辑
  const baselineText = typeof baselineData === 'string' ? baselineData :
    (baselineData.type === 'json_schema' ? 'JSON Schema Data' : String(baselineData));

  // 提取各解析器的文本
  const kdcMarkdownText = caseData.kdcMarkdown?.result?.data?.[0]?.markdown || '';
  const kdcPlainText = caseData.kdcPlain?.result?.data?.[0]?.plain || '';
  const kdcKdcText = caseData.kdcKdc?.result?.data?.[0]?.kdc || '';

  // MonkeyOCR (table)
  let monkeyOCRText = '';
  if (caseData.monkeyOCR?.result?.html) {
    monkeyOCRText = caseData.monkeyOCR.result.html;
  } else if (caseData.monkeyOCR?.html) {
    monkeyOCRText = caseData.monkeyOCR.html;
  }

  // MonkeyOCR (parse)
  let monkeyOCRV2Text = '';
  if (caseData.monkeyOCRV2?.result?.html) {
    monkeyOCRV2Text = caseData.monkeyOCRV2.result.html;
  } else if (caseData.monkeyOCRV2?.html) {
    monkeyOCRV2Text = caseData.monkeyOCRV2.html;
  }

  // VL LLM
  let vlLLMText = '';
  if (caseData.vlLLMResult?.result) {
    const result = caseData.vlLLMResult.result;
    let choices = [];
    if (result.content && result.content.choices) {
      choices = result.content.choices;
    } else if (result.choices) {
      choices = result.choices;
    }
    if (choices.length > 0) {
      vlLLMText = choices[0].message?.content || '';
    }
  }

  // MonkeyOCR Local
  let monkeyOCRLocalText = '';
  if (caseData.monkeyOCRLocal?.results?.[0]?.result) {
    const result = caseData.monkeyOCRLocal.results[0].result;
    monkeyOCRLocalText = result.html || result.markdown || '';
  } else if (caseData.monkeyOCRLocal?.result) {
    monkeyOCRLocalText = caseData.monkeyOCRLocal.result.html || caseData.monkeyOCRLocal.result.markdown || '';
  }

  // 计算各解析器的准确率
  const accuracies = {
    kdcMarkdown: calculateAccuracy(baselineData, kdcMarkdownText),
    kdcPlain: (useAnnotationAsBaseline && annotationData) || jsonSchemaData
      ? calculateAccuracy(baselineData, kdcPlainText)
      : 100, // KDC Plain作为基准时为100%
    kdcKdc: calculateAccuracy(baselineData, kdcKdcText),
    monkeyOCR: calculateAccuracy(baselineData, monkeyOCRText),
    monkeyOCRV2: calculateAccuracy(baselineData, monkeyOCRV2Text),
    vlLLM: calculateAccuracy(baselineData, vlLLMText),
    monkeyOCRLocal: calculateAccuracy(baselineData, monkeyOCRLocalText)
  };

  return {
    baselineData,
    baselineText,
    accuracies,
    texts: {
      kdcMarkdownText,
      kdcPlainText,
      kdcKdcText,
      monkeyOCRText,
      monkeyOCRV2Text,
      vlLLMText,
      monkeyOCRLocalText
    }
  };
};

/**
 * 解析表格内容为二维数组
 */
export const parseTableContent = (content) => {
  try {
    // 对于包含重复表格的内容，只取第一个表格
    let cleanContent = content;
    if (content.includes('<table')) {
      // 如果包含多个表格，检查是否是重复的
      const tableMatches = content.match(/<table[^>]*>.*?<\/table>/gs);
      if (tableMatches && tableMatches.length > 1) {
        console.log(`发现 ${tableMatches.length} 个表格，检查是否重复...`);

        // 检查是否是重复的表格（更严格的比较）
        const firstTable = tableMatches[0];
        const firstTableNormalized = normalizeTableContent(firstTable);

        let duplicateCount = 0;
        const isDuplicate = tableMatches.slice(1).every(table => {
          const tableNormalized = normalizeTableContent(table);
          const isMatch = tableNormalized === firstTableNormalized;
          if (isMatch) duplicateCount++;
          return isMatch;
        });

        if (isDuplicate && duplicateCount > 0) {
          console.log(`检测到 ${duplicateCount + 1} 个重复表格，使用第一个表格进行解析`);
          cleanContent = firstTable;
        } else {
          console.log('表格内容不同，保留所有表格');
          // 如果表格内容不同，可能需要合并或选择最完整的表格
          // 这里选择最长的表格作为主要内容
          const longestTable = tableMatches.reduce((longest, current) =>
            current.length > longest.length ? current : longest
          );
          cleanContent = longestTable;
        }
      }
      return parseHTMLTable(cleanContent);
    }

    // 尝试解析Markdown表格
    if (content.includes('|')) {
      return parseMarkdownTable(cleanContent);
    }

    return null;
  } catch (error) {
    console.warn('解析表格内容失败:', error);
    return null;
  }
};

/**
 * 标准化表格内容用于比较
 */
const normalizeTableContent = (tableHtml) => {
  return tableHtml
    .replace(/\s+/g, ' ')  // 将多个空白字符替换为单个空格
    .replace(/>\s+</g, '><')  // 移除标签间的空白
    .replace(/\s+>/g, '>')  // 移除标签前的空白
    .replace(/<\s+/g, '<')  // 移除标签后的空白
    .toLowerCase()
    .trim();
};

/**
 * 解析HTML表格
 */
const parseHTMLTable = (htmlContent) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  const table = doc.querySelector('table');

  if (!table) return null;

  const rows = [];
  const trs = table.querySelectorAll('tr');

  trs.forEach(tr => {
    const cells = [];
    const tds = tr.querySelectorAll('th, td');
    tds.forEach(td => {
      cells.push(td.textContent.trim());
    });
    if (cells.length > 0) {
      rows.push(cells);
    }
  });

  return rows;
};

/**
 * 解析Markdown表格
 */
const parseMarkdownTable = (markdownContent) => {
  const lines = markdownContent.split('\n').map(line => line.trim()).filter(line => line);
  const tableLines = lines.filter(line => line.startsWith('|') && line.endsWith('|'));

  if (tableLines.length < 2) return null;

  // 移除分隔符行
  const dataLines = tableLines.filter(line => !line.match(/^\|[\s\-|]+\|$/));

  const rows = [];
  dataLines.forEach(line => {
    const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
    if (cells.length > 0) {
      rows.push(cells);
    }
  });

  return rows;
};

/**
 * 移除表格尾部的空列
 */
const removeTrailingEmptyColumns = (table) => {
  if (!table || table.length === 0) return table;

  // 找到最大列数
  const maxCols = Math.max(...table.map(row => row.length));

  // 从右到左检查每一列是否为空
  let lastNonEmptyCol = -1;
  for (let col = 0; col < maxCols; col++) {
    let hasContent = false;
    for (let row = 0; row < table.length; row++) {
      const cell = (table[row][col] || '').trim();
      if (cell !== '') {
        hasContent = true;
        break;
      }
    }
    if (hasContent) {
      lastNonEmptyCol = col;
    }
  }

  // 如果所有列都为空，返回原表格
  if (lastNonEmptyCol === -1) return table;

  // 移除尾部空列
  return table.map(row => row.slice(0, lastNonEmptyCol + 1));
};

/**
 * 比较两个表格数据 - 按单元格位置进行精确比较
 */
const compareTableData = (table1, table2) => {
  if (!table1 || !table2) return 0;
  if (table1.length === 0 && table2.length === 0) return 100;
  if (table1.length === 0 || table2.length === 0) return 0;

  console.log('表格结构比较调试:', {
    table1Rows: table1.length,
    table2Rows: table2.length,
    table1Cols: table1[0] ? table1[0].length : 0,
    table2Cols: table2[0] ? table2[0].length : 0,
    table1Sample: table1.slice(0, 3).map(row => row.slice(0, 3)),
    table2Sample: table2.slice(0, 3).map(row => row.slice(0, 3))
  });

  // 获取两个表格的最大行数和列数
  const maxRows = Math.max(table1.length, table2.length);
  const maxCols = Math.max(
    Math.max(...table1.map(row => row.length)),
    Math.max(...table2.map(row => row.length))
  );

  let totalCells = 0;
  let matchedCells = 0;

  // 按位置比较每个单元格
  for (let i = 0; i < maxRows; i++) {
    for (let j = 0; j < maxCols; j++) {
      // 获取对应位置的单元格内容
      const cell1 = (table1[i] && table1[i][j] !== undefined) ?
        String(table1[i][j]).trim().toLowerCase() : '';
      const cell2 = (table2[i] && table2[i][j] !== undefined) ?
        String(table2[i][j]).trim().toLowerCase() : '';

      totalCells++;

      // 比较单元格内容
      if (cell1 === cell2) {
        matchedCells++;
      }

      // 调试前几个单元格的比较
      if (i < 3 && j < 3) {
        console.log(`单元格[${i}][${j}]比较:`, {
          cell1: cell1 || '(空)',
          cell2: cell2 || '(空)',
          match: cell1 === cell2
        });
      }
    }
  }

  const accuracy = totalCells > 0 ? (matchedCells / totalCells) * 100 : 0;

  console.log('表格单元格比较结果:', {
    totalCells,
    matchedCells,
    accuracy: accuracy.toFixed(2),
    maxRows,
    maxCols
  });

  return accuracy;
};

/**
 * 计算未命中的单元格详情
 */
export const getDiffCells = (gt_table, pred_table) => {
  if (!gt_table || !pred_table) return [];

  // 创建位置映射
  const gt_map = {};
  for (let i = 0; i < gt_table.length; i++) {
    for (let j = 0; j < gt_table[i].length; j++) {
      const cell = String(gt_table[i][j] || '').toLowerCase().trim();
      if (!gt_map[cell]) gt_map[cell] = [];
      gt_map[cell].push({ row: i, col: j });
    }
  }

  // 计算计数器
  const gt_flat = gt_table.flat().map(cell => String(cell || '').toLowerCase().trim());
  const pred_flat = pred_table.flat().map(cell => String(cell || '').toLowerCase().trim());

  const gt_counter = {};
  const pred_counter = {};

  gt_flat.forEach(cell => {
    gt_counter[cell] = (gt_counter[cell] || 0) + 1;
  });

  pred_flat.forEach(cell => {
    pred_counter[cell] = (pred_counter[cell] || 0) + 1;
  });

  // 计算命中计数器
  const hit_counter = {};
  for (const cell in gt_counter) {
    if (pred_counter[cell]) {
      hit_counter[cell] = Math.min(gt_counter[cell], pred_counter[cell]);
    }
  }

  // 计算未命中的单元格
  const missed = [];
  for (const cell in gt_counter) {
    const missed_count = gt_counter[cell] - (hit_counter[cell] || 0);
    for (let i = 0; i < missed_count; i++) {
      if (gt_map[cell] && gt_map[cell].length > 0) {
        const pos = gt_map[cell].shift();
        missed.push({
          content: cell,
          row: pos.row + 1, // 1-based
          col: pos.col + 1  // 1-based
        });
      }
    }
  }

  return missed;
};

/**
 * 计算JSON Schema格式数据的准确率
 * @param {Object} expectedJsonSchema - 期望的JSON Schema数据
 * @param {*} actual - 实际解析结果
 * @returns {number} 准确率百分比
 */
export const calculateJsonSchemaAccuracy = (expectedJsonSchema, actual) => {
  try {
    // 提取期望的表格内容
    const expectedContent = expectedJsonSchema.content;
    if (!expectedContent || expectedContent.length === 0) {
      return 0;
    }

    // 解析实际结果
    let actualTable = null;
    if (typeof actual === 'string') {
      // 尝试解析为表格
      actualTable = parseTableContent(actual);
    } else if (typeof actual === 'object' && actual.type === 'json_schema') {
      actualTable = actual.content;
    }

    if (!actualTable) {
      return 0;
    }

    // 使用改进的表格比较算法
    return compareJsonSchemaTableData(expectedContent, actualTable, expectedJsonSchema.structure);

  } catch (error) {
    console.warn('JSON Schema准确率计算失败:', error);
    return 0;
  }
};

/**
 * 比较JSON Schema表格数据
 * @param {Array} expectedTable - 期望的表格数据
 * @param {Array} actualTable - 实际的表格数据
 * @param {Object} expectedStructure - 期望的表格结构
 * @returns {number} 准确率百分比
 */
const compareJsonSchemaTableData = (expectedTable, actualTable, expectedStructure) => {
  if (!expectedTable || !actualTable) return 0;
  if (expectedTable.length === 0 && actualTable.length === 0) return 100;
  if (expectedTable.length === 0 || actualTable.length === 0) return 0;

  // 结构准确率权重
  const structureWeight = 0.3;
  const contentWeight = 0.7;

  // 计算结构准确率
  const structureAccuracy = calculateStructureAccuracy(expectedStructure, actualTable);

  // 计算内容准确率
  const contentAccuracy = calculateContentAccuracy(expectedTable, actualTable);

  // 综合准确率
  const overallAccuracy = structureAccuracy * structureWeight + contentAccuracy * contentWeight;

  console.log('JSON Schema准确率计算:', {
    structureAccuracy: structureAccuracy.toFixed(2),
    contentAccuracy: contentAccuracy.toFixed(2),
    overallAccuracy: overallAccuracy.toFixed(2)
  });

  return Math.round(overallAccuracy);
};

/**
 * 计算表格结构准确率
 * @param {Object} expectedStructure - 期望的表格结构
 * @param {Array} actualTable - 实际的表格数据
 * @returns {number} 结构准确率百分比
 */
const calculateStructureAccuracy = (expectedStructure, actualTable) => {
  if (!expectedStructure || !actualTable) return 0;

  let score = 0;
  let totalChecks = 0;

  // 检查行数
  totalChecks++;
  if (expectedStructure.rows === actualTable.length) {
    score++;
  }

  // 检查列数
  totalChecks++;
  const actualMaxCols = Math.max(...actualTable.map(row => row.length));
  if (expectedStructure.cols === actualMaxCols) {
    score++;
  }

  // 检查合并单元格（如果有的话）
  if (expectedStructure.mergedCells && expectedStructure.mergedCells.length > 0) {
    totalChecks++;
    // 这里可以添加更复杂的合并单元格检查逻辑
    // 暂时给予部分分数
    score += 0.5;
  }

  return totalChecks > 0 ? (score / totalChecks) * 100 : 0;
};

/**
 * 计算表格内容准确率
 * @param {Array} expectedTable - 期望的表格内容
 * @param {Array} actualTable - 实际的表格内容
 * @returns {number} 内容准确率百分比
 */
const calculateContentAccuracy = (expectedTable, actualTable) => {
  // 将表格数据扁平化为单元格数组
  const expectedCells = expectedTable.flat().map(cell =>
    String(cell || '').toLowerCase().trim()
  ).filter(cell => cell.length > 0);

  const actualCells = actualTable.flat().map(cell =>
    String(cell || '').toLowerCase().trim()
  ).filter(cell => cell.length > 0);

  if (expectedCells.length === 0 && actualCells.length === 0) return 100;
  if (expectedCells.length === 0 || actualCells.length === 0) return 0;

  // 使用改进的匹配算法
  return calculateCellMatchingAccuracy(expectedCells, actualCells);
};

/**
 * 计算单元格匹配准确率
 * @param {Array} expectedCells - 期望的单元格数组
 * @param {Array} actualCells - 实际的单元格数组
 * @returns {number} 匹配准确率百分比
 */
const calculateCellMatchingAccuracy = (expectedCells, actualCells) => {
  let totalScore = 0;
  let maxPossibleScore = expectedCells.length;

  expectedCells.forEach(expectedCell => {
    let bestMatch = 0;

    actualCells.forEach(actualCell => {
      // 计算单元格相似度
      const similarity = calculateCellSimilarity(expectedCell, actualCell);
      bestMatch = Math.max(bestMatch, similarity);
    });

    totalScore += bestMatch;
  });

  return maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;
};

/**
 * 计算单元格相似度
 * @param {string} cell1 - 单元格1
 * @param {string} cell2 - 单元格2
 * @returns {number} 相似度分数 (0-1)
 */
const calculateCellSimilarity = (cell1, cell2) => {
  if (cell1 === cell2) return 1.0;

  // 处理数字的相似性
  const num1 = parseFloat(cell1);
  const num2 = parseFloat(cell2);
  if (!isNaN(num1) && !isNaN(num2)) {
    if (num1 === num2) return 1.0;
    // 数字相近度计算
    const diff = Math.abs(num1 - num2);
    const avg = (Math.abs(num1) + Math.abs(num2)) / 2;
    if (avg === 0) return diff === 0 ? 1.0 : 0.0;
    return Math.max(0, 1 - (diff / avg));
  }

  // 文本相似度计算
  return calculateTextSimilarity(cell1, cell2);
};

/**
 * 计算文本相似度
 * @param {string} text1 - 文本1
 * @param {string} text2 - 文本2
 * @returns {number} 相似度分数 (0-1)
 */
const calculateTextSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0;

  // 完全匹配
  if (text1 === text2) return 1.0;

  // 包含关系
  if (text1.includes(text2) || text2.includes(text1)) {
    const shorter = text1.length < text2.length ? text1 : text2;
    const longer = text1.length >= text2.length ? text1 : text2;
    return shorter.length / longer.length;
  }

  // 字符级相似度
  const commonChars = countCommonCharacters(text1, text2);
  const maxLength = Math.max(text1.length, text2.length);

  return maxLength > 0 ? commonChars / maxLength : 0;
};

/**
 * 计算两个字符串的共同字符数
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 共同字符数
 */
const countCommonCharacters = (str1, str2) => {
  const chars1 = [...str1];
  const chars2 = [...str2];
  let commonCount = 0;

  chars1.forEach(char => {
    const index = chars2.indexOf(char);
    if (index !== -1) {
      chars2.splice(index, 1);
      commonCount++;
    }
  });

  return commonCount;
};

/**
 * 简单的准确率计算（备用方案）
 */
export const calculateAccuracySimple = (expected, actual) => {
  if (!expected || !actual) return 0;

  // 检查是否为解析失败的结果
  const actualStr = String(actual);
  if (actualStr.includes('MonkeyOCR处理失败') ||
      actualStr.includes('MonkeyOCR文件上传失败') ||
      actualStr.includes('MonkeyOCR处理超时') ||
      actualStr.includes('MonkeyOCR获取结果失败') ||
      actualStr.includes('MonkeyOCR处理请求提交失败') ||
      actualStr.includes('VL LLM处理失败') ||
      actualStr.includes('KDC处理失败')) {
    return 0;
  }

  // 简单的字符串相似度计算
  const expectedStr = String(expected).toLowerCase().trim();
  const actualStrLower = actualStr.toLowerCase().trim();

  if (expectedStr === actualStrLower) return 100;

  // 使用编辑距离计算相似度
  const distance = levenshteinDistance(expectedStr, actualStrLower);
  const maxLength = Math.max(expectedStr.length, actualStrLower.length);

  if (maxLength === 0) return 100;

  return Math.max(0, ((maxLength - distance) / maxLength) * 100);
};

/**
 * 计算编辑距离
 */
const levenshteinDistance = (str1, str2) => {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
};

/**
 * 处理解析结果数据，整合6路解析结果
 * 基于images目录中的图片文件，使用文件名匹配而不是索引匹配
 * 因为不同解析器的处理顺序可能不一致
 */
export const processParseResults = (parseData, imageList = []) => {
  if (!parseData) return [];

  // 从parse_results对象中提取实际的解析结果，使用正确的字段名
  const parseResults = parseData.parse_results || {};
  const {
    kdc_markdown: kdc_results = [],           // KDC Markdown结果
    kdc_plain: kdc_plain_results = [],        // KDC Plain结果  
    kdc_kdc: kdc_kdc_results = [],           // KDC KDC结果
    monkey_ocr: monkey_ocr_results = [],      // MonkeyOCR结果
    monkey_ocr_latex: monkey_ocr_results_v2 = [], // MonkeyOCR Latex结果
    vl_llm: vl_llm_results = [],             // VL LLM结果
    monkey_ocr_local: monkey_ocr_local_results = [] // MonkeyOCR Local结果
  } = parseResults;

  // 同时保持对旧格式的兼容性（直接从parseData提取）
  const {
    uploaded_files = [],
    kdc_results: legacy_kdc_results = [],
    kdc_plain_results: legacy_kdc_plain_results = [],
    kdc_kdc_results: legacy_kdc_kdc_results = [],
    monkey_ocr_results: legacy_monkey_ocr_results = [],
    monkey_ocr_results_v2: legacy_monkey_ocr_results_v2 = [],
    vl_llm_results: legacy_vl_llm_results = [],
    monkey_ocr_local_results: legacy_monkey_ocr_local_results = []
  } = parseData;

  // 使用新格式数据，如果为空则回退到旧格式
  const finalKdcResults = kdc_results.length > 0 ? kdc_results : legacy_kdc_results;
  const finalKdcPlainResults = kdc_plain_results.length > 0 ? kdc_plain_results : legacy_kdc_plain_results;
  const finalKdcKdcResults = kdc_kdc_results.length > 0 ? kdc_kdc_results : legacy_kdc_kdc_results;
  const finalMonkeyOcrResults = monkey_ocr_results.length > 0 ? monkey_ocr_results : legacy_monkey_ocr_results;
  const finalMonkeyOcrResultsV2 = monkey_ocr_results_v2.length > 0 ? monkey_ocr_results_v2 : legacy_monkey_ocr_results_v2;
  const finalVlLlmResults = vl_llm_results.length > 0 ? vl_llm_results : legacy_vl_llm_results;
  const finalMonkeyOcrLocalResults = monkey_ocr_local_results.length > 0 ? monkey_ocr_local_results : legacy_monkey_ocr_local_results;

  // 如果没有提供imageList，回退到uploaded_files
  const sourceList = imageList.length > 0 ? imageList : uploaded_files;
  
  // 确保sourceList按文件名排序，与后端处理顺序保持一致
  const sortedSourceList = Array.isArray(sourceList) ? 
    sourceList.slice().sort((a, b) => {
      const aName = typeof a === 'string' ? a : (a.original_image || a.fname || a.filename || a.name || '');
      const bName = typeof b === 'string' ? b : (b.original_image || b.fname || b.filename || b.name || '');
      return aName.localeCompare(bName);
    }) : sourceList;

  console.log('Processing parse data:', {
    imageList: imageList.length,
    uploaded_files: uploaded_files.length,
    sortedSourceList: sortedSourceList.length,
    kdc_results: finalKdcResults.length,
    kdc_plain_results: finalKdcPlainResults.length,
    kdc_kdc_results: finalKdcKdcResults.length,
    monkey_ocr_results: finalMonkeyOcrResults.length,
    monkey_ocr_results_v2: finalMonkeyOcrResultsV2.length,
    vl_llm_results: finalVlLlmResults.length,
    monkey_ocr_local_results: finalMonkeyOcrLocalResults.length
  });

  // 创建文件名到解析结果的映射，以便快速查找
  // 使用多种可能的文件名格式进行匹配
  const createFileNameMap = (results, getFileNameFn, getOriginalImageFn) => {
    const map = new Map();
    results.forEach(result => {
      // 尝试多种文件名提取方式
      const fileName = getFileNameFn(result);
      const originalImage = getOriginalImageFn ? getOriginalImageFn(result) : null;
      
      if (fileName) {
        const baseName = getImageBaseName(fileName);
        map.set(baseName, result);
        
        // 也尝试完整文件名
        map.set(fileName, result);
        map.set(fileName.replace(/\.(pdf|png|jpg|jpeg)$/i, ''), result);
      }
      
      if (originalImage) {
        const baseName = getImageBaseName(originalImage);
        map.set(baseName, result);
        map.set(originalImage, result);
        map.set(originalImage.replace(/\.(pdf|png|jpg|jpeg)$/i, ''), result);
      }
    });
    return map;
  };

  // 创建各路解析结果的文件名映射
  const kdcMap = createFileNameMap(finalKdcResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const kdcPlainMap = createFileNameMap(finalKdcPlainResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const kdcKdcMap = createFileNameMap(finalKdcKdcResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrMap = createFileNameMap(finalMonkeyOcrResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrV2Map = createFileNameMap(finalMonkeyOcrResultsV2, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const vlLlmMap = createFileNameMap(finalVlLlmResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrLocalMap = createFileNameMap(finalMonkeyOcrLocalResults, 
    r => r.filename || r.fname, 
    r => r.original_image);

  console.log('Created file name maps:', {
    kdcMap: kdcMap.size,
    kdcPlainMap: kdcPlainMap.size,
    kdcKdcMap: kdcKdcMap.size,
    monkeyOcrMap: monkeyOcrMap.size,
    monkeyOcrV2Map: monkeyOcrV2Map.size,
    vlLlmMap: vlLlmMap.size,
    monkeyOcrLocalMap: monkeyOcrLocalMap.size
  });

  return sortedSourceList.map((item, index) => {
    // 如果是图片文件名，直接使用；如果是uploaded_files对象，提取文件名
    const imageName = typeof item === 'string' ? item : (item.original_image || item.fname || item.filename || item.name || `file_${index}`);
    const fileName = imageName;
    const baseName = getImageBaseName(fileName);
    const pdfBaseName = baseName; // PDF通常使用相同的基础名称

    console.log(`Processing file ${index}:`, { imageName, fileName, baseName, pdfBaseName });

    // 使用文件名匹配查找各路解析结果
    // 尝试多种可能的匹配方式：baseName, fileName, pdfBaseName等
    const findResult = (map, targetBaseName, targetFileName) => {
      // 优先使用baseName匹配
      let result = map.get(targetBaseName);
      if (result) return result;
      
      // 尝试完整文件名
      result = map.get(targetFileName);
      if (result) return result;
      
      // 尝试PDF文件名
      result = map.get(targetBaseName + '.pdf');
      if (result) return result;
      
      // 尝试原始图片名
      result = map.get(targetFileName.replace(/\.(pdf)$/i, '.png'));
      if (result) return result;
      
      return null;
    };

    const kdcResult = findResult(kdcMap, baseName, fileName);
    const kdcPlainResult = findResult(kdcPlainMap, baseName, fileName);
    const kdcKdcResult = findResult(kdcKdcMap, baseName, fileName);
    const monkeyResult = findResult(monkeyOcrMap, baseName, fileName);
    const monkeyResultV2 = findResult(monkeyOcrV2Map, baseName, fileName);
    const vlLLMResult = findResult(vlLlmMap, baseName, fileName);
    const monkeyOCRLocalResult = findResult(monkeyOcrLocalMap, baseName, fileName);

    console.log(`Found results for ${imageName} (baseName: ${baseName}):`, {
      kdc: !!kdcResult,
      kdcPlain: !!kdcPlainResult,
      kdcKdc: !!kdcKdcResult,
      monkeyOCR: !!monkeyResult,
      monkeyOCRV2: !!monkeyResultV2,
      vlLLM: !!vlLLMResult,
      monkeyOCRLocal: !!monkeyOCRLocalResult
    });

    // 调试VL LLM数据
    if (vlLLMResult) {
      console.log('VL LLM Result found:', vlLLMResult.filename, 'hasResult:', !!vlLLMResult.result);
    }

    // 调试信息：显示匹配到的文件名
    if (kdcResult) {
      console.log(`  KDC matched: ${kdcResult.filename || kdcResult.fname} -> ${kdcResult.original_image || 'N/A'}`);
    }
    if (vlLLMResult) {
      console.log(`  VL LLM matched: ${vlLLMResult.filename || vlLLMResult.fname} -> ${vlLLMResult.original_image || 'N/A'}`);
    }

    // 提取KDC KDC的特征信息
    const kdcFeatures = kdcKdcResult && kdcKdcResult.features ? kdcKdcResult.features : null;

    return {
      id: `case_${index}`,
      index: index + 1, // 显示用的1基索引
      fileName: imageName, // 使用原始图片文件名
      baseName,
      imageName, // 原始图片文件名
      pdfFileName: baseName + '.pdf', // 对应的PDF文件名
      file: typeof item === 'string' ? { name: item } : item, // 兼容原有结构
      // KDC Markdown (原kdc_results)
      kdcMarkdown: kdcResult || null,
      // KDC Plain
      kdcPlain: kdcPlainResult || null,
      // KDC KDC
      kdcKdc: kdcKdcResult || null,
      // MonkeyOCR (table prompt)
      monkeyOCR: monkeyResult || null,
      // MonkeyOCR (parse)
      monkeyOCRV2: monkeyResultV2 || null,
      // VL LLM
      vlLLMResult: vlLLMResult || null,
      // MonkeyOCR (local)
      monkeyOCRLocal: monkeyOCRLocalResult || null,
      // 特征信息
      features: {
        kdc: kdcFeatures
      }
    };
  });
};

/**
 * 提取文本内容用于比较
 */
export const extractTextContent = (data) => {
  if (!data) return '';

  if (typeof data === 'string') return data;

  if (data.markdown) return data.markdown;
  if (data.plain) return data.plain;
  if (data.text) return data.text;
  if (data.content) return data.content;

  return JSON.stringify(data);
};

/**
 * 按照原始HTML报告的逻辑提取各路解析结果的文本内容
 * 用于在原始文本列显示
 */
export const extractParseResultText = (data, type = 'markdown') => {
  if (!data) return '';

  switch (type) {
    case 'markdown':
      // KDC Markdown结果提取 和 本地MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 本地MonkeyOCR结果格式：{ results: [{ filename: "xxx", result: { html: "xxx", markdown: "xxx", type: "html" } }] }
          if (data.results && Array.isArray(data.results) && data.results.length > 0) {
            const firstResult = data.results[0];
            if (firstResult.result && typeof firstResult.result === 'object') {
              // 检查success字段
              if (firstResult.result.success === false) {
                return `MonkeyOCR Local处理失败: ${firstResult.result.html || firstResult.result.markdown || '未知错误'}`;
              }
              
              // 优先使用 html 内容，因为 type 通常是 "html"
              if (firstResult.result.html) {
                return firstResult.result.html;
              }
              if (firstResult.result.markdown) {
                return firstResult.result.markdown;
              }
            }
          }
          // 兼容旧格式：单个result字段
          if (data.result && typeof data.result === 'object') {
            // 优先使用 html 内容，因为 type 通常是 "html"
            if (data.result.html) {
              return data.result.html;
            }
            if (data.result.markdown) {
              return data.result.markdown;
            }
            // 新格式：KDC类型的数据在result.data中
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].markdown || '';
            }
          }
          // 兼容旧格式
          if (data.result && typeof data.result === 'string') {
            return data.result;
          }
          // 兼容旧格式：KDC Markdown结果格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].markdown || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.markdown || '';
          }
        } catch (e) {
          console.error('提取markdown内容失败:', e);
        }
      }
      return '';

    case 'plain':
      // KDC Plain结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].plain || '';
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].plain || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.plain || '';
          }
        } catch (e) {
          console.error('提取plain内容失败:', e);
        }
      }
      return '';

    case 'kdc':
      // KDC KDC结果提取 - 按照HTML报告的逻辑，原始文本显示完整的JSON数据
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              const docData = resultData[0].doc || {};
              if (docData && typeof docData === 'object') {
                return JSON.stringify(docData, null, 2);
              }
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          let docData = null;
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            docData = dataArray[0].doc || {};
          } else if (typeof dataArray === 'object') {
            docData = dataArray.doc || {};
          }

          if (docData) {
            // 返回格式化的JSON字符串，用于原始文本显示
            return JSON.stringify(docData, null, 2);
          }
        } catch (e) {
          console.error('提取kdc内容失败:', e);
        }
      }
      return '';

    case 'html':
      // MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        // 检查新格式的success字段
        if (data.result && data.result.success === false) {
          return `MonkeyOCR处理失败: ${data.result.html || '未知错误'}`;
        }
        
        // 检查是否超时
        if (data.result && data.result.is_timeout) {
          const processing_time = data.result.processing_time || 0;
          return `MonkeyOCR处理超时 (${processing_time.toFixed(1)}秒)`;
        }

        // MonkeyOCR结果格式：{ filename: "xxx", result: { html: "xxx", text_content: [...] } }
        if (data.result) {
          // 优先使用result.html字段
          if (data.result.html) {
            return data.result.html;
          }
          // 如果没有html字段，尝试使用result.text_content的第一个元素
          if (data.result.text_content && Array.isArray(data.result.text_content) && data.result.text_content.length > 0) {
            return data.result.text_content[0];
          }
        }
        // 兼容旧格式：直接在data层级
        if (data.html) {
          return data.html;
        }
        if (data.text_content && Array.isArray(data.text_content) && data.text_content.length > 0) {
          return data.text_content[0];
        }
      }
      return '';

    case 'vl_llm':
      // VL LLM结果提取 - 处理两种不同的数据结构
      if (typeof data === 'object') {
        try {
          const result = data.result || {};

          // 尝试两种不同的数据结构
          let choices = [];

          // 结构1: result.content.choices (tables数据集)
          if (result.content && result.content.choices) {
            choices = result.content.choices;
            console.log('VL LLM using structure 1 (result.content.choices)');
          }
          // 结构2: result.choices (kingsoft数据集)
          else if (result.choices) {
            choices = result.choices;
            console.log('VL LLM using structure 2 (result.choices)');
          }

          if (choices.length > 0) {
            const message = choices[0].message || {};
            let content = message.content || '';

            // 清理markdown代码块标记
            if (content.startsWith('```markdown')) {
              content = content.replace('```markdown', '').replace(/```$/, '').trim();
            } else if (content.startsWith('```')) {
              content = content.replace(/^```/, '').replace(/```$/, '').trim();
            }

            console.log('VL LLM content extracted successfully, length:', content.length);
            return content;
          }
        } catch (e) {
          console.error('提取VL LLM内容失败:', e);
        }
      }
      return '';

    default:
      return typeof data === 'string' ? data : JSON.stringify(data, null, 2);
  }
};

/**
 * 提取用于渲染的数据内容
 * 与extractParseResultText不同，这个函数返回适合TableRenderer渲染的数据
 */
export const extractParseResultForRender = (data, type = 'markdown') => {
  if (!data) return '';

  switch (type) {
    case 'markdown':
      // KDC Markdown结果提取 和 本地MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 本地MonkeyOCR结果格式：{ results: [{ filename: "xxx", result: { html: "xxx", markdown: "xxx", type: "html" } }] }
          if (data.results && Array.isArray(data.results) && data.results.length > 0) {
            const firstResult = data.results[0];
            if (firstResult.result && typeof firstResult.result === 'object') {
              // 检查success字段
              if (firstResult.result.success === false) {
                return `MonkeyOCR Local处理失败: ${firstResult.result.html || firstResult.result.markdown || '未知错误'}`;
              }
              
              if (firstResult.result.type === 'latex') {
                return firstResult.result.latex || firstResult.result.html;  // 对于LaTeX格式，优先返回latex内容
              }
              if (firstResult.result.type === 'html') {
                return firstResult.result.html;  // 对于HTML格式，返回html内容
              }
              if (firstResult.result.markdown) {
                return firstResult.result.markdown;
              }
              if (firstResult.result.html) {
                return firstResult.result.html;
              }
            }
          }
          // 兼容旧格式和KDC类型
          if (data.result && typeof data.result === 'object') {
            if (data.result.type === 'latex') {
              return data.result.latex || data.result.html;  // 对于LaTeX格式，优先返回latex内容
            }
            if (data.result.type === 'html') {
              return data.result.html;  // 对于HTML格式，返回html内容
            }
            if (typeof data.result === 'string') {
              return data.result;
            }
            if (data.result.markdown) {
              return data.result.markdown;
            }
            if (data.result.html) {
              return data.result.html;
            }
            // 新格式：KDC类型的数据在result.data中
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].markdown || '';
            }
          }
          // 兼容旧格式：KDC Markdown结果格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].markdown || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.markdown || '';
          }
        } catch (e) {
          console.error('提取markdown内容失败:', e);
        }
      }
      return '';

    case 'latex':
      // LaTeX格式提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        // MonkeyOCR Local 结果中的 LaTeX
        if (data.results && Array.isArray(data.results) && data.results.length > 0) {
          const firstResult = data.results[0];
          if (firstResult.result && firstResult.result.latex) {
            return firstResult.result.latex;
          }
          if (firstResult.result && firstResult.result.html) {
            return firstResult.result.html;
          }
        }
        // 旧格式
        if (data.result && data.result.html) {
          return data.result.html;
        }
      }
      return '';

    case 'plain':
      // KDC Plain结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].plain || '';
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].plain || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.plain || '';
          }
        } catch (e) {
          console.error('提取plain内容失败:', e);
        }
      }
      return '';

    case 'kdc':
      // KDC KDC结果提取 - 返回Canvas渲染器期望的数据格式
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              const docData = resultData[0].doc || {};
              if (docData && typeof docData === 'object') {
                // 返回Canvas渲染器期望的格式: { data: [{ doc: ... }] }
                return { data: [{ doc: docData }] };
              }
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          let docData = null;
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            docData = dataArray[0].doc || {};
          } else if (typeof dataArray === 'object') {
            docData = dataArray.doc || {};
          }

          if (docData) {
            // 返回Canvas渲染器期望的格式: { data: [{ doc: ... }] }
            return { data: [{ doc: docData }] };
          }
        } catch (e) {
          console.error('提取kdc内容失败:', e);
        }
      }
      return '';

    case 'html':
      // MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        // 检查新格式的success字段
        if (data.result && data.result.success === false) {
          return `MonkeyOCR处理失败: ${data.result.html || '未知错误'}`;
        }
        
        // 检查是否超时
        if (data.result && data.result.is_timeout) {
          const processing_time = data.result.processing_time || 0;
          return `MonkeyOCR处理超时 (${processing_time.toFixed(1)}秒)`;
        }

        if (data.result) {
          if (data.result.html) {
            return data.result.html;
          }
          if (data.result.text_content && Array.isArray(data.result.text_content) && data.result.text_content.length > 0) {
            return data.result.text_content[0];
          }
        }
        if (data.html) {
          return data.html;
        }
        if (data.text_content && Array.isArray(data.text_content) && data.text_content.length > 0) {
          return data.text_content[0];
        }
      }
      return '';

    case 'vl_llm':
      // VL LLM结果 - 处理两种不同的数据结构
      if (typeof data === 'object') {
        try {
          const result = data.result || {};

          // 尝试两种不同的数据结构
          let choices = [];

          // 结构1: result.content.choices (tables数据集)
          if (result.content && result.content.choices) {
            choices = result.content.choices;
          }
          // 结构2: result.choices (kingsoft数据集)
          else if (result.choices) {
            choices = result.choices;
          }

          if (choices.length > 0) {
            const message = choices[0].message || {};
            let content = message.content || '';

            // 清理markdown代码块标记
            if (content.startsWith('```markdown')) {
              content = content.replace('```markdown', '').replace(/```$/, '').trim();
            } else if (content.startsWith('```')) {
              content = content.replace(/^```/, '').replace(/```$/, '').trim();
            }

            return content;
          }
        } catch (e) {
          console.error('提取VL LLM渲染内容失败:', e);
        }
      }
      return '';

    default:
      return typeof data === 'string' ? data : data;
  }
};

/**
 * 从KDC文档结构中提取文本内容的辅助函数
 */
export const extractTextFromKdcDoc = (docData) => {
  if (!docData || typeof docData !== 'object') return '';

  try {
    // 递归提取所有文本内容
    const extractText = (obj) => {
      if (typeof obj === 'string') return obj;
      if (Array.isArray(obj)) {
        return obj.map(extractText).join(' ');
      }
      if (typeof obj === 'object' && obj !== null) {
        if (obj.text) return obj.text;
        if (obj.content) return extractText(obj.content);
        if (obj.children) return extractText(obj.children);
        // 递归处理所有属性
        return Object.values(obj).map(extractText).filter(Boolean).join(' ');
      }
      return '';
    };

    return extractText(docData);
  } catch (e) {
    console.error('从KDC文档提取文本失败:', e);
    return JSON.stringify(docData, null, 2);
  }
};

/**
 * 改进的表格数据比较算法
 * 更好地处理表格结构差异和内容匹配
 */
const compareTableDataImproved = (table1, table2) => {
  if (!table1 || !table2) return 0;
  if (table1.length === 0 && table2.length === 0) return 100;
  if (table1.length === 0 || table2.length === 0) return 0;

  // 使用与现有compareTableData相同的算法，但添加更多调试信息
  const gt_flat = table1.flat().map(cell => String(cell || '').toLowerCase().trim()).filter(cell => cell.length > 0);
  const pred_flat = table2.flat().map(cell => String(cell || '').toLowerCase().trim()).filter(cell => cell.length > 0);

  console.log('改进表格比较调试:', {
    table1Rows: table1.length,
    table2Rows: table2.length,
    gt_flatCells: gt_flat.length,
    pred_flatCells: pred_flat.length,
    gt_sample: gt_flat.slice(0, 5),
    pred_sample: pred_flat.slice(0, 5)
  });

  if (!gt_flat.length && !pred_flat.length) return 100;
  if (!gt_flat.length || !pred_flat.length) return 0;

  // 计算每个单元格的出现次数
  const gt_counter = {};
  const pred_counter = {};

  gt_flat.forEach(cell => {
    gt_counter[cell] = (gt_counter[cell] || 0) + 1;
  });

  pred_flat.forEach(cell => {
    pred_counter[cell] = (pred_counter[cell] || 0) + 1;
  });

  // 计算命中数量（取最小值）
  let hit = 0;
  for (const cell in gt_counter) {
    if (pred_counter[cell]) {
      hit += Math.min(gt_counter[cell], pred_counter[cell]);
    }
  }

  const total = gt_flat.length;
  const accuracy = total > 0 ? (hit / total) * 100 : 0;

  console.log('改进表格匹配结果:', {
    hit,
    total,
    accuracy: accuracy.toFixed(2),
    matchedCells: Object.keys(gt_counter).filter(cell => pred_counter[cell]).length,
    totalUniqueCells: Object.keys(gt_counter).length
  });

  return accuracy;
};

/**
 * 改进的文本准确率计算
 * 更好地处理HTML和Markdown格式的差异
 */
const calculateAccuracyImproved = (expected, actual) => {
  if (!expected || !actual) return 0;

  // 提取纯文本内容
  const extractTextContent = (content) => {
    let text = String(content);

    // 移除HTML标签
    text = text.replace(/<[^>]*>/g, ' ');

    // 移除Markdown标记
    text = text.replace(/\|/g, ' ');
    text = text.replace(/[-:]+/g, ' ');
    text = text.replace(/\*\*/g, '');
    text = text.replace(/\*/g, '');
    text = text.replace(/#/g, '');

    // 标准化空白字符
    text = text.replace(/\s+/g, ' ').trim();

    return text.toLowerCase();
  };

  const expectedText = extractTextContent(expected);
  const actualText = extractTextContent(actual);

  console.log('改进文本内容比较:', {
    expectedLength: expectedText.length,
    actualLength: actualText.length,
    expectedSample: expectedText.substring(0, 100),
    actualSample: actualText.substring(0, 100)
  });

  if (expectedText === actualText) return 100;
  if (!expectedText || !actualText) return 0;

  // 使用词汇级别的比较
  const expectedWords = expectedText.split(/\s+/).filter(w => w.length > 0);
  const actualWords = actualText.split(/\s+/).filter(w => w.length > 0);

  if (expectedWords.length === 0 && actualWords.length === 0) return 100;
  if (expectedWords.length === 0 || actualWords.length === 0) return 0;

  // 计算词汇匹配度
  const totalWords = expectedWords.length; // 以期望文本为基准
  let matchedWords = 0;

  const actualWordsSet = new Set(actualWords);

  for (const word of expectedWords) {
    if (actualWordsSet.has(word)) {
      matchedWords++;
    }
  }

  const accuracy = (matchedWords / totalWords) * 100;
  console.log('改进文本匹配结果:', {
    expectedWords: expectedWords.length,
    actualWords: actualWords.length,
    matchedWords,
    totalWords,
    accuracy: accuracy.toFixed(2)
  });

  return accuracy;
};
