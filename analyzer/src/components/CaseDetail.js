import React, { useState, useEffect, memo } from 'react';
import { extractParseResultForRender, calculateAccuracyForCase } from '../utils/dataProcessor';
import { getDatasetAnnotations } from '../services/api';
import ImageModal from './ImageModal';
import TableRenderer from './TableRenderer';
import JsonViewer from './JsonViewer';
import CollapsibleText from './CollapsibleText';
import TextboxDistributionChart from './TextboxDistributionChart';
import MetricsPanel from './MetricsPanel';
import AccuracyReport from './AccuracyReport';
import AccuracySummaryChart from './AccuracySummaryChart';
import './CaseDetail.css';

const CaseDetail = memo(({ caseData, dataset }) => {
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [annotationData, setAnnotationData] = useState(null);
  const [useAnnotationAsBaseline, setUseAnnotationAsBaseline] = useState(true);
  const [showOriginalText, setShowOriginalText] = useState(false); // 默认隐藏原始文本列

  // 获取标注数据
  useEffect(() => {
    const loadAnnotationData = async () => {
      if (!caseData || !caseData.fileName) {
        setAnnotationData(null);
        return;
      }

      try {
        // 优先使用传入的dataset参数
        let datasetName = dataset;

        // 如果没有传入dataset，尝试从其他地方获取
        if (!datasetName) {
          // 尝试从localStorage获取
          const storedDataset = localStorage.getItem('selectedDataset');
          if (storedDataset) {
            datasetName = storedDataset;
          }

          // 尝试从URL获取
          const urlParams = new URLSearchParams(window.location.search);
          const urlDataset = urlParams.get('dataset');
          if (urlDataset) {
            datasetName = urlDataset;
          }

          // 最后的默认值
          if (!datasetName) {
            datasetName = 'test20';
          }
        }

        console.log('开始获取标注数据:', {
          datasetName,
          fileName: caseData.fileName
        });

        const annotations = await getDatasetAnnotations(datasetName);

        // 查找匹配的标注
        const matchingAnnotation = annotations.find(ann =>
          ann.image_filename === caseData.fileName
        );

        console.log('查找标注数据:', {
          datasetName,
          fileName: caseData.fileName,
          totalAnnotations: annotations.length,
          annotationFiles: annotations.map(a => a.image_filename)
        });

        if (matchingAnnotation) {
          setAnnotationData(matchingAnnotation);
          console.log('✅ 找到标注数据:', matchingAnnotation);
        } else {
          setAnnotationData(null);
          console.log('❌ 未找到匹配的标注数据:', caseData.fileName);
        }
      } catch (error) {
        console.error('获取标注数据失败:', error);
        setAnnotationData(null);
      }
    };

    loadAnnotationData();
  }, [caseData, dataset]); // 添加dataset依赖

  if (!caseData) {
    return (
      <div className="case-detail">
        <div className="case-detail-empty">
          请选择一个测试案例查看详情
        </div>
      </div>
    );
  }

  const {
    index,
    fileName,
    baseName,
    dataset: caseDataset,
    hasImage,
    imagePath,
    kdcMarkdown,
    kdcPlain,
    kdcKdc,
    monkeyOCR,
    monkeyOCRV2,
    vlLLMResult,
    monkeyOCRLocal,
    features
  } = caseData;

  // 使用新的imagePath字段
  const imageUrl = imagePath;

  const handleImageClick = () => {
    if (imageUrl) {
      setSelectedImage(imageUrl);
      setImageModalOpen(true);
    }
  };

  const closeImageModal = () => {
    setImageModalOpen(false);
    setSelectedImage(null);
  };

  // 调试信息：显示各路解析结果的匹配状态
  const debugInfo = {
    kdcMarkdown: kdcMarkdown ? { filename: kdcMarkdown.filename, hasData: !!kdcMarkdown.data } : null,
    kdcPlain: kdcPlain ? { filename: kdcPlain.filename, hasData: !!kdcPlain.data } : null,
    kdcKdc: kdcKdc ? { filename: kdcKdc.filename, hasData: !!kdcKdc.data } : null,
    monkeyOCR: monkeyOCR ? { filename: monkeyOCR.filename, hasResult: !!monkeyOCR.result } : null,
    monkeyOCRV2: monkeyOCRV2 ? { filename: monkeyOCRV2.filename, hasResult: !!monkeyOCRV2.result } : null,
    vlLLM: vlLLMResult ? { filename: vlLLMResult.filename, originalImage: vlLLMResult.original_image, hasResult: !!vlLLMResult.result } : null,
    monkeyOCRLocal: monkeyOCRLocal ? { filename: monkeyOCRLocal.filename, originalImage: monkeyOCRLocal.original_image, hasResult: !!monkeyOCRLocal.result } : null
  };

  // 使用统一的准确率计算函数，基于人工标注数据
  const accuracyData = calculateAccuracyForCase(
    caseData,
    annotationData,
    useAnnotationAsBaseline
  );
  const { baselineText, accuracies, texts } = accuracyData;

  // 解构准确率数据
  const {
    kdcMarkdown: kdcMarkdownAccuracy,
    kdcPlain: kdcPlainAccuracy,
    kdcKdc: kdcKdcAccuracy,
    monkeyOCR: monkeyOCRAccuracy,
    monkeyOCRV2: monkeyOCRV2Accuracy,
    vlLLM: vlLLMAccuracy,
    monkeyOCRLocal: monkeyOCRLocalAccuracy
  } = accuracies;

  // 解构文本数据（用于原始文本列显示）
  const {
    kdcMarkdownText,
    kdcPlainText,
    kdcKdcText,
    monkeyOCRText,
    monkeyOCRV2Text,
    vlLLMText,
    monkeyOCRLocalText
  } = texts;

  // 提取渲染数据内容 - 用于渲染结果列显示
  const kdcMarkdownRenderData = extractParseResultForRender(kdcMarkdown, 'markdown');
  const kdcPlainRenderData = extractParseResultForRender(kdcPlain, 'plain');
  const kdcKdcRenderData = extractParseResultForRender(kdcKdc, 'kdc'); // 使用extractParseResultForRender处理KDC数据
  const monkeyOCRRenderData = extractParseResultForRender(monkeyOCR, 'html');
  const monkeyOCRV2RenderData = extractParseResultForRender(monkeyOCRV2, 'html');
  const vlLLMRenderData = extractParseResultForRender(vlLLMResult, 'vl_llm');
  const monkeyOCRLocalRenderData = extractParseResultForRender(monkeyOCRLocal, monkeyOCRLocal?.result?.type || 'markdown');

  // 调试KDC渲染数据
  console.log('KDC Render Data Debug:', {
    kdcKdc,
    kdcKdcRenderData,
    kdcKdcType: typeof kdcKdcRenderData,
    kdcKdcHasData: !!(kdcKdcRenderData && kdcKdcRenderData.data)
  });

  // 调试信息 - 在所有变量定义之后
  console.log('CaseDetail Debug:', {
    fileName,
    dataset: caseDataset,
    hasImage,
    imageUrl,
    debugInfo,
    caseData
  });




  return (
    <div className="case-detail">
      <div className="case-detail-header">
        <h3>案例详情 - #{index}</h3>
        <div className="case-detail-filename">{fileName}</div>
      </div>

      {/* 数据信息块 */}
      <div className="case-info-block">
        <div className="case-info-header">
          <h4>案例信息</h4>
        </div>
        <div className="case-info-content">
          <div className="sequence-number">#{index}</div>
          <div className="file-info">
            <div className="file-name">{fileName}</div>
            <div className="base-name">基础名: {baseName}</div>
          </div>

          {imageUrl ? (
            <div className="image-preview-container">
              <img
                src={imageUrl}
                alt={fileName}
                className="image-preview"
                onClick={handleImageClick}
                onLoad={() => console.log('图片加载成功:', imageUrl)}
                onError={(e) => {
                  console.error('图片加载失败:', imageUrl);
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
              />
              <div style={{ display: 'none', color: '#dc3545', fontSize: '12px', textAlign: 'center', padding: '20px' }}>
                图片加载失败: {imageUrl}
              </div>
            </div>
          ) : (
            <div className="image-preview-container">
              <div style={{ color: '#666', fontSize: '12px', textAlign: 'center', padding: '20px' }}>
                无图片
              </div>
            </div>
          )}


          {/* 人工标注数据渲染结果 */}
          {annotationData && (
            <div className="">
              <div className="cell-header">
                人工标注数据
              </div>
              <div className={`cell-content has-table`}>
                <TableRenderer
                  content={annotationData ? JSON.parse(annotationData.table_content) : null}
                  type="json_schema"
                  placeholder="无标注数据"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 调试信息面板 */}
      <div className="case-info-block" style={{ backgroundColor: '#f8f9fa', border: '1px solid #e9ecef' }}>
        <div className="case-info-header">
          <h4 style={{ color: '#666' }}>🔍 调试信息 (验证数据匹配)</h4>
        </div>
        <div style={{ padding: '16px', fontSize: '12px', fontFamily: 'monospace' }}>
          <div style={{ marginBottom: '12px' }}>
            <strong>当前案例:</strong> {fileName} (索引: {index})
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '12px' }}>
            {Object.entries(debugInfo).map(([key, info]) => (
              <div key={key} style={{ 
                padding: '8px', 
                backgroundColor: info ? '#d4edda' : '#f8d7da', 
                border: `1px solid ${info ? '#c3e6cb' : '#f5c6cb'}`,
                borderRadius: '4px'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{key}:</div>
                {info ? (
                  <div>
                    <div>✅ 有数据</div>
                    {info.filename && <div>文件: {info.filename}</div>}
                    {info.originalImage && <div>图片: {info.originalImage}</div>}
                    {info.hasData !== undefined && <div>数据: {info.hasData ? '是' : '否'}</div>}
                    {info.hasResult !== undefined && <div>结果: {info.hasResult ? '是' : '否'}</div>}
                  </div>
                ) : (
                  <div>❌ 无数据</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 解析状态块 */}
      <div className="parsing-status-block">
        <h3>解析状态</h3>
        <div className="status-indicators-grid">
          <div className={`status-indicator ${kdcMarkdown ? 'success' : 'error'}`} title="KDC Markdown">
            <span className="status-name">KDC Markdown</span>
            <span className="status-icon">{kdcMarkdown ? '✓' : '✗'}</span>
          </div>
          <div className={`status-indicator ${kdcPlain ? 'success' : 'error'}`} title="KDC Plain">
            <span className="status-name">KDC Plain</span>
            <span className="status-icon">{kdcPlain ? '✓' : '✗'}</span>
          </div>
          <div className={`status-indicator ${kdcKdc ? 'success' : 'error'}`} title="KDC KDC">
            <span className="status-name">KDC KDC</span>
            <span className="status-icon">{kdcKdc ? '✓' : '✗'}</span>
          </div>
          <div className={`status-indicator ${monkeyOCR ? 'success' : 'error'}`} title="MonkeyOCR(HTML)">
            <span className="status-name">MonkeyOCR (HTML)</span>
            <span className="status-icon">{monkeyOCR ? '✓' : '✗'}</span>
          </div>
          <div className={`status-indicator ${monkeyOCRV2 ? 'success' : 'error'}`} title="MonkeyOCR(LaTeX)">
            <span className="status-name">MonkeyOCR (LaTeX)</span>
            <span className="status-icon">{monkeyOCRV2 ? '✓' : '✗'}</span>
          </div>
          <div className={`status-indicator ${vlLLMResult ? 'success' : 'error'}`} title="VL-LLM">
            <span className="status-name">VL-LLM</span>
            <span className="status-icon">{vlLLMResult ? '✓' : '✗'}</span>
          </div>
          <div className={`status-indicator ${monkeyOCRLocal ? 'success' : 'error'}`} title="MonkeyOCR(local)">
            <span className="status-name">MonkeyOCR (local)</span>
            <span className="status-icon">{monkeyOCRLocal ? '✓' : '✗'}</span>
          </div>
        </div>
      </div>

      {/* 统计结果汇总块 */}
      {/* 准确率汇总图表 */}
      <AccuracySummaryChart
        caseData={caseData}
        index={index}
        fileName={fileName}
        annotationData={annotationData}
        useAnnotationAsBaseline={useAnnotationAsBaseline}
      />

      {/* 基准选择控制 */}
      {annotationData && (
        <div className="baseline-control-block">
          <div className="baseline-toggle">
            <label>
              <input
                type="checkbox"
                checked={useAnnotationAsBaseline}
                onChange={() => setUseAnnotationAsBaseline(!useAnnotationAsBaseline)}
              />
              使用人工标注数据作为基准
            </label>
            <div className="baseline-info">
              {useAnnotationAsBaseline ?
                '当前基准: 人工标注数据' :
                '当前基准: KDC Plain'
              }
            </div>
          </div>
        </div>
      )}

      {!annotationData && (
        <div className="baseline-control-block">
          <div className="baseline-info">
            当前基准: KDC Plain（无人工标注数据）
          </div>
        </div>
      )}

      {/* 特征信息和指标信息并列块 */}
      {features && features.kdc && (
        <div className="features-metrics-container">
          {/* 特征信息块 */}
          <div className="features-summary-block">
            <div className="features-summary-header">
              <h4>特征信息</h4>
            </div>
            <div className="features-summary-content">
              <div className="feature-item">
                <div className="feature-label">KDC Bbox数量</div>
                <div className="feature-value">{features.kdc.bbox_count || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均X坐标</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_x || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均Y坐标</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_y || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均宽度</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_width || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均高度</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_height || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">Bbox密度</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.bbox_density || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">文本框数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.textbox || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">表格数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.table || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">组件数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.component || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">表格单元格数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.table_cell || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">检测到表格</div>
                <div className={`feature-value ${features.kdc.table_detection?.has_table ? 'feature-value-success' : 'feature-value-warning'}`}>
                  {features.kdc.table_detection?.has_table ? '是' : '否'}
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-label">表格检测数量</div>
                <div className="feature-value">{features.kdc.table_detection?.table_count || 0}</div>
              </div>
            </div>

            {/* 文本框数量分布图表 */}
            {features.kdc.textbox_count_distribution && (
              <TextboxDistributionChart
                distributionData={features.kdc.textbox_count_distribution}
                className="textbox-distribution-feature"
              />
            )}
          </div>

          {/* 指标信息块 */}
          <div className="metrics-summary-block">
            <MetricsPanel caseData={caseData} />
          </div>
        </div>
      )}

      {/* 六路解析详情块 */}
      <div className="parsing-details-block">
        <div className="parsing-details-header">
          <h4>解析详情</h4>
          <div className="parsing-details-controls">
            <button
              className="toggle-original-text-btn"
              onClick={() => setShowOriginalText(!showOriginalText)}
              title={showOriginalText ? "隐藏原始文本列" : "显示原始文本列"}
            >
              <span className={`toggle-arrow ${showOriginalText ? 'expanded' : 'collapsed'}`}>
                ▶
              </span>
              {showOriginalText ? '隐藏原始文本' : '显示原始文本'}
            </button>
          </div>
        </div>
        <div style={{ overflowX: 'auto', width: '100%' }}>
          <div className={`case-detail-content ${!showOriginalText ? 'hide-original-text' : ''}`}>
            {/* 第一行：MonkeyOCR（parse） */}
            <div className="case-detail-row case-detail-row-1">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">MonkeyOCR（parse）原始文本</div>
                  <div className="cell-content">
                    <CollapsibleText
                      content={monkeyOCRV2Text}
                      placeholder="无MonkeyOCR(parse)数据"
                      maxLines={3}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">MonkeyOCR（parse）渲染结果</div>
                <div className={`cell-content ${monkeyOCRV2RenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={monkeyOCRV2RenderData}
                    type="html"
                    placeholder="无MonkeyOCR(parse)渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="monkeyocr-parse"
                    baselineText={baselineText}
                    actualText={monkeyOCRV2Text}
                    accuracy={monkeyOCRV2Accuracy}
                    parserName="MonkeyOCR(parse)"
                    hasTable={!!monkeyOCRV2RenderData}
                    isTimeout={monkeyOCRV2?.result?.is_timeout}
                    processingTime={monkeyOCRV2?.result?.processing_time}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>

            {/* 第二行：MonkeyOCR (table prompt) */}
            <div className="case-detail-row case-detail-row-2">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">MonkeyOCR（table prompt）原始文本</div>
                  <div className="cell-content">
                    <CollapsibleText
                      content={monkeyOCRText}
                      placeholder="无MonkeyOCR(table)数据"
                      maxLines={3}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">MonkeyOCR（table prompt）渲染结果</div>
                <div className={`cell-content ${monkeyOCRRenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={monkeyOCRRenderData}
                    type="html"
                    placeholder="无MonkeyOCR渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="monkeyocr-table"
                    baselineText={baselineText}
                    actualText={monkeyOCRText}
                    accuracy={monkeyOCRAccuracy}
                    parserName="MonkeyOCR(table)"
                    hasTable={!!monkeyOCRRenderData}
                    isTimeout={monkeyOCR?.result?.is_timeout}
                    processingTime={monkeyOCR?.result?.processing_time}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>

            {/* 第三行：MonkeyOCR (local) */}
            <div className="case-detail-row case-detail-row-3">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">MonkeyOCR (local) 原始文本</div>
                  <div className="cell-content">
                    <CollapsibleText
                      content={monkeyOCRLocalText}
                      placeholder="无MonkeyOCR(local)数据"
                      maxLines={3}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">MonkeyOCR (local) 渲染结果</div>
                <div className={`cell-content ${monkeyOCRLocalRenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={monkeyOCRLocalRenderData}
                    type={monkeyOCRLocal?.result?.type || "html"}
                    placeholder="无MonkeyOCR (local)渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="monkeyocr-local"
                    baselineText={baselineText}
                    actualText={monkeyOCRLocalText}
                    accuracy={monkeyOCRLocalAccuracy}
                    parserName="MonkeyOCR(local)"
                    hasTable={!!monkeyOCRLocalRenderData}
                    isTimeout={false}
                    processingTime={null}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>

            {/* 第四行：VL LLM */}
            <div className="case-detail-row case-detail-row-4">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">VL LLM 原始文本</div>
                  <div className="cell-content">
                    <CollapsibleText
                      content={vlLLMText}
                      placeholder="无VL LLM数据"
                      maxLines={3}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">VL LLM 渲染结果</div>
                <div className={`cell-content ${vlLLMRenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={vlLLMRenderData}
                    type="markdown"
                    placeholder="无VL LLM渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="vl-llm"
                    baselineText={baselineText}
                    actualText={vlLLMText}
                    accuracy={vlLLMAccuracy}
                    parserName="VL LLM"
                    hasTable={!!vlLLMRenderData}
                    isTimeout={false}
                    processingTime={null}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>

            {/* 第五行：KDC Plain */}
            <div className="case-detail-row case-detail-row-5">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">KDC Plain 原始文本</div>
                  <div className="cell-content">
                    <CollapsibleText
                      content={kdcPlainText}
                      placeholder="无KDC Plain数据"
                      maxLines={3}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">KDC Plain 渲染结果</div>
                <div className={`cell-content ${kdcPlainRenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={kdcPlainRenderData}
                    type="plain"
                    placeholder="无KDC Plain渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="kdc-plain"
                    baselineText={baselineText}
                    actualText={kdcPlainText}
                    accuracy={kdcPlainAccuracy}
                    parserName="KDC Plain"
                    hasTable={!!kdcPlainRenderData}
                    isTimeout={false}
                    processingTime={null}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>

            {/* 第六行：KDC Markdown */}
            <div className="case-detail-row case-detail-row-6">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">KDC Markdown 原始文本</div>
                  <div className="cell-content">
                    <CollapsibleText
                      content={kdcMarkdownText}
                      placeholder="无KDC Markdown数据"
                      maxLines={3}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">KDC Markdown 渲染结果</div>
                <div className={`cell-content ${kdcMarkdownRenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={kdcMarkdownRenderData}
                    type="markdown"
                    placeholder="无KDC Markdown渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="kdc-markdown"
                    baselineText={baselineText}
                    actualText={kdcMarkdownText}
                    accuracy={kdcMarkdownAccuracy}
                    parserName="KDC Markdown"
                    hasTable={!!kdcMarkdownRenderData}
                    isTimeout={false}
                    processingTime={null}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>

            {/* 第七行：KDC KDC */}
            <div className="case-detail-row case-detail-row-7">
              {showOriginalText && (
                <div className="case-detail-cell case-detail-original-text">
                  <div className="cell-header">KDC KDC 原始文本</div>
                  <div className={`cell-content ${kdcKdcText ? 'has-content' : ''}`}>
                    <JsonViewer
                      data={kdcKdcRenderData}
                      placeholder="无KDC KDC数据"
                      collapsed={true}
                    />
                  </div>
                </div>
              )}

              <div className="case-detail-cell case-detail-rendered-result">
                <div className="cell-header">KDC KDC 渲染结果</div>
                <div className={`cell-content ${kdcKdcRenderData ? 'has-table' : ''}`}>
                  <TableRenderer
                    content={kdcKdcRenderData}
                    type="kdc"
                    placeholder="无KDC KDC渲染结果"
                  />
                </div>
              </div>

              <div className="case-detail-cell case-detail-accuracy-report">
                <div className="cell-header">准确率报告</div>
                <div className="cell-content">
                  <AccuracyReport
                    key="kdc-kdc"
                    baselineText={baselineText}
                    actualText={kdcKdcText}
                    accuracy={kdcKdcAccuracy}
                    parserName="KDC KDC"
                    hasTable={!!kdcKdcRenderData}
                    isTimeout={false}
                    processingTime={null}
                    hasAnnotationData={!!annotationData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {imageModalOpen && (
        <ImageModal
          imageUrl={selectedImage}
          onClose={closeImageModal}
          title={fileName}
        />
      )}
    </div>
  );
});

export default CaseDetail;
