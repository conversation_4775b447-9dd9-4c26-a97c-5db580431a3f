import { useState, useMemo } from 'react';
import './JsonViewer.css';

const JsonViewer = ({ data, placeholder = "无JSON数据", collapsed = true }) => {
  const [isCollapsed, setIsCollapsed] = useState(collapsed);

  const jsonData = useMemo(() => {
    if (!data) return null;

    try {
      // 如果已经是对象，直接使用
      if (typeof data === 'object') {
        return data;
      }
      // 如果是字符串，尝试解析
      if (typeof data === 'string') {
        return JSON.parse(data);
      }
      return null;
    } catch (e) {
      console.error('JSON解析失败:', e);
      return null;
    }
  }, [data]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  if (!jsonData) {
    return (
      <div className="json-viewer json-viewer-empty">
        <div className="json-viewer-placeholder">{placeholder}</div>
      </div>
    );
  }

  return (
    <div className="json-viewer">
      <div className="json-viewer-header">
        <button
          className="json-viewer-toggle"
          onClick={toggleCollapse}
          title={isCollapsed ? "展开JSON" : "收缩JSON"}
        >
          <span className={`json-viewer-arrow ${isCollapsed ? 'collapsed' : 'expanded'}`}>
            ▶
          </span>
          JSON数据 ({isCollapsed ? '已收缩' : '已展开'})
        </button>
      </div>

      {!isCollapsed && (
        <div className="json-viewer-content">
          <JsonValue
            value={jsonData}
            level={0}
          />
        </div>
      )}
    </div>
  );
};

// 独立的JSON值渲染组件
const JsonValue = ({ value, level = 0 }) => {
  if (value === null) {
    return <span className="json-null">null</span>;
  }

  if (typeof value === 'boolean') {
    return <span className="json-boolean">{value.toString()}</span>;
  }

  if (typeof value === 'number') {
    return <span className="json-number">{value}</span>;
  }

  if (typeof value === 'string') {
    return <span className="json-string">"{value}"</span>;
  }

  if (Array.isArray(value)) {
    return <JsonArray value={value} level={level} />;
  }

  if (typeof value === 'object' && value !== null) {
    return <JsonObject value={value} level={level} />;
  }

  return <span className="json-unknown">{String(value)}</span>;
};

// 独立的JSON数组组件
const JsonArray = ({ value, level }) => {
  const [collapsed, setCollapsed] = useState(level > 2); // 默认展开前3层
  const indent = level * 20;

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  if (value.length === 0) {
    return <span className="json-array-empty">[]</span>;
  }

  return (
    <div className="json-array">
      <div className="json-node-header">
        <button
          className="json-node-toggle"
          onClick={toggleCollapse}
          style={{ marginLeft: `${indent}px` }}
        >
          <span className={`json-arrow ${collapsed ? 'collapsed' : 'expanded'}`}>
            ▶
          </span>
          <span className="json-bracket">[</span>
          <span className="json-array-length">({value.length} items)</span>
          {collapsed && <span className="json-bracket">]</span>}
        </button>
      </div>

      {!collapsed && (
        <div className="json-array-content">
          {value.map((item, index) => (
            <div key={index} className="json-array-item">
              <div style={{ marginLeft: `${indent + 20}px` }}>
                <span className="json-array-index">[{index}]:</span>
                <JsonValue value={item} level={level + 1} />
                {index < value.length - 1 && <span className="json-comma">,</span>}
              </div>
            </div>
          ))}
          <div style={{ marginLeft: `${indent}px` }}>
            <span className="json-bracket">]</span>
          </div>
        </div>
      )}
    </div>
  );
};

// 独立的JSON对象组件
const JsonObject = ({ value, level }) => {
  const [collapsed, setCollapsed] = useState(level > 2); // 默认展开前3层
  const indent = level * 20;

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const keys = Object.keys(value);
  if (keys.length === 0) {
    return <span className="json-object-empty">{"{}"}</span>;
  }

  return (
    <div className="json-object">
      <div className="json-node-header">
        <button
          className="json-node-toggle"
          onClick={toggleCollapse}
          style={{ marginLeft: `${indent}px` }}
        >
          <span className={`json-arrow ${collapsed ? 'collapsed' : 'expanded'}`}>
            ▶
          </span>
          <span className="json-bracket">{"{"}</span>
          <span className="json-object-length">({keys.length} keys)</span>
          {collapsed && <span className="json-bracket">{"}"}</span>}
        </button>
      </div>

      {!collapsed && (
        <div className="json-object-content">
          {keys.map((objKey, index) => (
            <div key={objKey} className="json-object-item">
              <div style={{ marginLeft: `${indent + 20}px` }}>
                <span className="json-key">"{objKey}"</span>
                <span className="json-colon">: </span>
                <JsonValue value={value[objKey]} level={level + 1} />
                {index < keys.length - 1 && <span className="json-comma">,</span>}
              </div>
            </div>
          ))}
          <div style={{ marginLeft: `${indent}px` }}>
            <span className="json-bracket">{"}"}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default JsonViewer;