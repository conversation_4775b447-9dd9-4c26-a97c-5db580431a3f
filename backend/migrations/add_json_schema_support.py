#!/usr/bin/env python3
"""
数据库迁移脚本：添加JSON Schema支持
更新标注表以支持JSON Schema格式的数据
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from sqlalchemy import text
from backend.database.database import get_database, engine

def migrate_annotation_table():
    """迁移标注表以支持JSON Schema"""
    print("开始迁移标注表...")
    
    try:
        with engine.connect() as conn:
            # 检查是否已经有json_schema类型的标注
            result = conn.execute(text("""
                SELECT COUNT(*) as count 
                FROM tablerag_annotations 
                WHERE annotation_type = 'json_schema'
            """))
            
            existing_count = result.fetchone()[0]
            print(f"当前已有 {existing_count} 条JSON Schema类型的标注")
            
            # 更新annotation_type字段的注释
            conn.execute(text("""
                ALTER TABLE tablerag_annotations 
                MODIFY COLUMN annotation_type VARCHAR(50) 
                COMMENT 'manual/imported/json_schema/auto_generated'
            """))
            
            # 更新table_content字段的注释
            conn.execute(text("""
                ALTER TABLE tablerag_annotations 
                MODIFY COLUMN table_content TEXT 
                COMMENT '表格内容（JSON Schema格式或传统格式）'
            """))
            
            conn.commit()
            print("✅ 标注表迁移完成")
            
    except Exception as e:
        print(f"❌ 标注表迁移失败: {e}")
        raise

def create_json_schema_indexes():
    """创建JSON Schema相关的索引"""
    print("创建JSON Schema相关索引...")
    
    try:
        with engine.connect() as conn:
            # 为annotation_type字段创建索引（如果不存在）
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_annotations_type 
                ON tablerag_annotations(annotation_type)
            """))
            
            conn.commit()
            print("✅ 索引创建完成")
            
    except Exception as e:
        print(f"❌ 索引创建失败: {e}")
        raise

def migrate_existing_data():
    """迁移现有数据"""
    print("检查现有数据...")
    
    try:
        with engine.connect() as conn:
            # 统计各种类型的标注数量
            result = conn.execute(text("""
                SELECT annotation_type, COUNT(*) as count 
                FROM tablerag_annotations 
                GROUP BY annotation_type
            """))
            
            type_counts = result.fetchall()
            print("现有标注类型统计:")
            for type_name, count in type_counts:
                print(f"  - {type_name}: {count} 条")
            
            # 检查是否有需要转换的数据
            result = conn.execute(text("""
                SELECT COUNT(*) as count 
                FROM tablerag_annotations 
                WHERE annotation_type = 'auto_generated' 
                AND table_content LIKE '%{%'
                AND table_content LIKE '%}%'
            """))
            
            potential_json_count = result.fetchone()[0]
            if potential_json_count > 0:
                print(f"发现 {potential_json_count} 条可能是JSON格式的auto_generated标注")
                
                # 可以选择性地将这些标注标记为json_schema类型
                # 这里先不自动转换，避免误操作
                print("建议手动检查这些数据是否需要转换为json_schema类型")
            
            print("✅ 数据检查完成")
            
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")
        raise

def verify_migration():
    """验证迁移结果"""
    print("验证迁移结果...")
    
    try:
        with engine.connect() as conn:
            # 检查表结构
            result = conn.execute(text("""
                DESCRIBE tablerag_annotations
            """))
            
            columns = result.fetchall()
            print("标注表当前结构:")
            for column in columns:
                print(f"  - {column[0]}: {column[1]} {column[2] or ''}")
            
            # 检查索引
            result = conn.execute(text("""
                SHOW INDEX FROM tablerag_annotations
            """))
            
            indexes = result.fetchall()
            print("标注表索引:")
            for index in indexes:
                print(f"  - {index[2]}: {index[4]}")
            
            print("✅ 迁移验证完成")
            
    except Exception as e:
        print(f"❌ 迁移验证失败: {e}")
        raise

def main():
    """主迁移函数"""
    print("开始JSON Schema支持迁移")
    print("="*50)
    
    try:
        # 执行迁移步骤
        migrate_annotation_table()
        create_json_schema_indexes()
        migrate_existing_data()
        verify_migration()
        
        print("\n" + "="*50)
        print("🎉 JSON Schema支持迁移完成！")
        print("\n后续步骤:")
        print("1. 重启后端服务以应用数据库更改")
        print("2. 使用新的JSON Schema格式创建标注数据")
        print("3. 测试准确率计算功能")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        print("\n请检查:")
        print("1. 数据库连接是否正常")
        print("2. 是否有足够的数据库权限")
        print("3. 数据库表是否存在")
        
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
