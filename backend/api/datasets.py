"""
数据集管理API

提供数据集的增删改查功能，替代原有的目录扫描方式
"""

import os
from pathlib import Path
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.database.database import get_database
from backend.database.crud import DatasetCRUD, ImageCRUD

router = APIRouter()

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

class DatasetResponse(BaseModel):
    """数据集响应模型"""
    id: int
    name: str
    description: str = None
    image_count: int = 0
    created_at: str

class DatasetCreate(BaseModel):
    """创建数据集请求模型"""
    name: str
    description: str = None

@router.get("/datasets", response_model=List[DatasetResponse])
async def get_datasets(db: Session = Depends(get_database)):
    """
    获取数据集列表
    
    替代原有的目录扫描方式，从数据库获取数据集信息
    同时扫描文件系统，自动同步新的数据集
    """
    try:
        # 扫描文件系统中的数据集目录
        dataset_dir = PROJECT_ROOT / "dataset"
        if dataset_dir.exists():
            for item in dataset_dir.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    # 检查数据库中是否已存在
                    existing = DatasetCRUD.get_by_name(db, item.name)
                    if not existing:
                        # 自动创建数据集记录
                        DatasetCRUD.create(db, item.name, f"自动发现的数据集: {item.name}")
                        
                        # 同时扫描并创建图片记录
                        images_dir = item / "images"
                        if images_dir.exists():
                            image_files = []
                            for img_file in images_dir.iterdir():
                                if img_file.is_file() and img_file.suffix.lower() in ['.png', '.jpg', '.jpeg', '.gif', '.webp']:
                                    image_files.append({
                                        "filename": img_file.name,
                                        "file_path": str(img_file.relative_to(PROJECT_ROOT))
                                    })
                            
                            if image_files:
                                dataset_record = DatasetCRUD.get_by_name(db, item.name)
                                ImageCRUD.bulk_create(db, dataset_record.id, image_files)
        
        # 从数据库获取所有数据集
        datasets = DatasetCRUD.get_all(db)
        
        # 构建响应数据
        result = []
        for dataset in datasets:
            result.append(DatasetResponse(
                id=dataset.id,
                name=dataset.name,
                description=dataset.description or "",
                image_count=len(dataset.images),
                created_at=dataset.created_at.isoformat()
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据集列表失败: {str(e)}")

@router.post("/datasets", response_model=DatasetResponse)
async def create_dataset(dataset: DatasetCreate, db: Session = Depends(get_database)):
    """创建新数据集"""
    try:
        # 检查是否已存在
        existing = DatasetCRUD.get_by_name(db, dataset.name)
        if existing:
            raise HTTPException(status_code=400, detail=f"数据集 '{dataset.name}' 已存在")
        
        # 创建数据集记录
        new_dataset = DatasetCRUD.create(db, dataset.name, dataset.description)
        
        # 创建文件系统目录
        dataset_dir = PROJECT_ROOT / "dataset" / dataset.name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        (dataset_dir / "images").mkdir(exist_ok=True)
        
        return DatasetResponse(
            id=new_dataset.id,
            name=new_dataset.name,
            description=new_dataset.description or "",
            image_count=0,
            created_at=new_dataset.created_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建数据集失败: {str(e)}")

@router.get("/datasets/{dataset_name}")
async def get_dataset_info(dataset_name: str, db: Session = Depends(get_database)):
    """获取数据集详细信息"""
    try:
        dataset = DatasetCRUD.get_by_name(db, dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集 '{dataset_name}' 不存在")
        
        return DatasetResponse(
            id=dataset.id,
            name=dataset.name,
            description=dataset.description or "",
            image_count=len(dataset.images),
            created_at=dataset.created_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据集信息失败: {str(e)}")

@router.delete("/datasets/{dataset_name}")
async def delete_dataset(dataset_name: str, db: Session = Depends(get_database)):
    """删除数据集"""
    try:
        dataset = DatasetCRUD.get_by_name(db, dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集 '{dataset_name}' 不存在")
        
        # 删除数据库记录（级联删除相关数据）
        DatasetCRUD.delete(db, dataset.id)
        
        return {"message": f"数据集 '{dataset_name}' 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除数据集失败: {str(e)}")

@router.get("/datasets/{dataset_name}/json-schema-data")
async def get_dataset_json_schema_data(dataset_name: str):
    """获取数据集的JSON Schema数据"""
    try:
        import json

        # 尝试从多个可能的位置查找JSON Schema数据
        possible_paths = [
            PROJECT_ROOT / "dataset" / dataset_name / "gen_data" / "llm_gen_data.json",
            PROJECT_ROOT / "generator" / "data" / "llm_train_data" / "latest" / "llm_gen_data.json",
            PROJECT_ROOT / "generator" / "data" / "llm_train_data" / "2025-07-02_2" / "llm_gen_data.json"
        ]

        for json_path in possible_paths:
            if json_path.exists():
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    if isinstance(data, list) and len(data) > 0:
                        return data

                except Exception as e:
                    print(f"读取JSON Schema文件失败 {json_path}: {e}")
                    continue

        # 如果没有找到JSON Schema数据，返回空数组
        return []

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取JSON Schema数据失败: {str(e)}")
