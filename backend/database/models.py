"""
数据库模型定义

定义标注系统的数据表结构
"""

from sqlalchemy import Column, Integer, String, Text, Float, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

Base = declarative_base()

class Dataset(Base):
    """数据集表"""
    __tablename__ = "tablerag_datasets"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    # 关系
    images = relationship("Image", back_populates="dataset", cascade="all, delete-orphan")

class Image(Base):
    """图片表"""
    __tablename__ = "tablerag_images"
    
    id = Column(Integer, primary_key=True, index=True)
    dataset_id = Column(Integer, ForeignKey("tablerag_datasets.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    # 关系
    dataset = relationship("Dataset", back_populates="images")
    annotations = relationship("Annotation", back_populates="image", cascade="all, delete-orphan")
    evaluations = relationship("AccuracyEvaluation", back_populates="image", cascade="all, delete-orphan")

class Annotation(Base):
    """标注表（人工标注的标准答案）"""
    __tablename__ = "tablerag_annotations"

    id = Column(Integer, primary_key=True, index=True)
    image_id = Column(Integer, ForeignKey("tablerag_images.id"), nullable=False)
    annotator = Column(String(100), nullable=False)  # 标注员
    table_structure = Column(Text, nullable=False)    # 表格结构（JSON格式）
    table_content = Column(Text, nullable=False)      # 表格内容（JSON Schema格式）
    annotation_type = Column(String(50), default="manual")  # manual/imported/json_schema
    status = Column(String(20), default="draft")     # draft/completed/reviewed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 关系
    image = relationship("Image", back_populates="annotations")
    evaluations = relationship("AccuracyEvaluation", back_populates="annotation", cascade="all, delete-orphan")

class AccuracyEvaluation(Base):
    """准确率评估结果表"""
    __tablename__ = "tablerag_accuracy_evaluations"
    
    id = Column(Integer, primary_key=True, index=True)
    image_id = Column(Integer, ForeignKey("tablerag_images.id"), nullable=False)
    annotation_id = Column(Integer, ForeignKey("tablerag_annotations.id"), nullable=False)
    parser_name = Column(String(100), nullable=False)  # kdc_markdown, monkey_ocr等
    structure_accuracy = Column(Float)                  # 表格结构准确率
    content_accuracy = Column(Float)                    # 内容准确率
    overall_accuracy = Column(Float)                    # 综合准确率
    evaluation_details = Column(Text)                   # 详细评估信息（JSON）
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    # 关系
    image = relationship("Image", back_populates="evaluations")
    annotation = relationship("Annotation", back_populates="evaluations")

class Report(Base):
    """报告表"""
    __tablename__ = "tablerag_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    dataset_name = Column(String(100), nullable=False)
    report_type = Column(String(50), nullable=False)   # accuracy/comparison/summary
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)             # 报告内容（HTML/JSON）
    file_path = Column(String(500))                    # 报告文件路径
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    created_by = Column(String(100))                   # 创建者
