#!/usr/bin/env python3
"""
修复wi数据集KDC解析结果内容与文件名匹配关系的脚本
"""

import json
import os
import shutil
from datetime import datetime
from collections import OrderedDict

def analyze_original_order():
    """分析原始的解析顺序和文件映射关系"""
    print("🔍 分析原始解析顺序...")
    
    # 检查原始图片文件的顺序
    images_dir = "dataset/wi/images"
    if not os.path.exists(images_dir):
        print(f"❌ 图片目录不存在: {images_dir}")
        return None, None
    
    # 获取所有图片文件并排序（这是原始的处理顺序）
    image_files = sorted([f for f in os.listdir(images_dir) 
                         if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))])
    
    print(f"📸 原始图片文件顺序 (共{len(image_files)}个):")
    for i, img in enumerate(image_files[:10]):
        print(f"   {i+1:2d}. {img}")
    if len(image_files) > 10:
        print(f"   ... 还有 {len(image_files) - 10} 个文件")
    
    # 创建图片 -> PDF的映射
    original_pdf_order = []
    for img_file in image_files:
        pdf_name = f"{os.path.splitext(img_file)[0]}.pdf"
        original_pdf_order.append(pdf_name)
    
    return image_files, original_pdf_order

def fix_wi_content_mapping():
    """修复wi数据集的内容映射关系"""
    print("🔧 修复wi数据集的内容映射关系...")
    
    # 分析原始顺序
    image_files, original_pdf_order = analyze_original_order()
    if not image_files:
        return False
    
    # 读取当前的解析结果
    parse_results_file = "parse_results/wi/latest_results.json"
    if not os.path.exists(parse_results_file):
        print(f"❌ 解析结果文件不存在: {parse_results_file}")
        return False
    
    with open(parse_results_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 当前数据概况:")
    print(f"   原始图片文件: {len(image_files)}")
    print(f"   处理文件: {len(data.get('processed_files', []))}")
    print(f"   KDC结果: {len(data.get('kdc_results', []))}")
    
    # 备份原始文件
    backup_file = f"parse_results/wi/before_content_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    shutil.copy2(parse_results_file, backup_file)
    print(f"📦 原始文件已备份到: {backup_file}")
    
    # 获取各种解析结果
    kdc_results = data.get('kdc_results', [])
    kdc_plain_results = data.get('kdc_plain_results', [])
    kdc_kdc_results = data.get('kdc_kdc_results', [])
    monkey_ocr_results = data.get('monkey_ocr_results', [])
    monkey_ocr_results_v2 = data.get('monkey_ocr_results_v2', [])
    vl_llm_results = data.get('vl_llm_results', [])
    
    print(f"\n📋 各类解析结果数量:")
    print(f"   KDC: {len(kdc_results)}")
    print(f"   KDC Plain: {len(kdc_plain_results)}")
    print(f"   KDC KDC: {len(kdc_kdc_results)}")
    print(f"   MonkeyOCR: {len(monkey_ocr_results)}")
    print(f"   MonkeyOCR V2: {len(monkey_ocr_results_v2)}")
    print(f"   VL LLM: {len(vl_llm_results)}")
    
    # 重新建立正确的映射关系
    print(f"\n🔧 重新建立正确的内容映射关系...")
    
    def rebuild_results_mapping(results_list, result_type_name):
        """重建结果映射关系"""
        if not results_list:
            return []
        
        print(f"   📝 重建{result_type_name}映射...")
        
        # 保存原始的解析内容（按当前顺序）
        original_contents = []
        for result in results_list:
            if isinstance(result, dict):
                # 提取实际的解析内容，保留其他元数据
                content_copy = result.copy()
                original_contents.append(content_copy)
            else:
                original_contents.append(result)
        
        # 创建新的映射关系
        new_results = []
        for i, pdf_name in enumerate(original_pdf_order):
            if i < len(original_contents):
                # 获取原始内容
                content = original_contents[i]
                
                # 查找对应的图片文件名
                img_name = image_files[i] if i < len(image_files) else None
                
                # 创建正确映射的结果
                if isinstance(content, dict):
                    fixed_result = content.copy()
                    fixed_result["filename"] = pdf_name
                    fixed_result["original_image"] = img_name
                    
                    # 查找对应的file_id
                    for pf in data.get('processed_files', []):
                        if pf.get('fname') == pdf_name:
                            fixed_result["file_id"] = str(pf.get('id', ''))
                            break
                else:
                    fixed_result = content
                
                new_results.append(fixed_result)
            else:
                # 如果内容不够，创建空结果
                new_results.append({
                    "filename": pdf_name,
                    "original_image": img_name,
                    "file_id": "",
                    "error": "Missing content"
                })
        
        print(f"     ✅ {result_type_name}: {len(original_contents)} -> {len(new_results)}")
        return new_results
    
    # 重建所有结果的映射
    fixed_kdc_results = rebuild_results_mapping(kdc_results, "KDC")
    fixed_kdc_plain_results = rebuild_results_mapping(kdc_plain_results, "KDC Plain")
    fixed_kdc_kdc_results = rebuild_results_mapping(kdc_kdc_results, "KDC KDC")
    fixed_monkey_ocr_results = rebuild_results_mapping(monkey_ocr_results, "MonkeyOCR")
    fixed_monkey_ocr_results_v2 = rebuild_results_mapping(monkey_ocr_results_v2, "MonkeyOCR V2")
    fixed_vl_llm_results = rebuild_results_mapping(vl_llm_results, "VL LLM")
    
    # 更新数据
    data["kdc_results"] = fixed_kdc_results
    data["kdc_plain_results"] = fixed_kdc_plain_results
    data["kdc_kdc_results"] = fixed_kdc_kdc_results
    data["monkey_ocr_results"] = fixed_monkey_ocr_results
    data["monkey_ocr_results_v2"] = fixed_monkey_ocr_results_v2
    data["vl_llm_results"] = fixed_vl_llm_results
    
    # 添加修复标记
    if "metadata" not in data:
        data["metadata"] = {}
    data["metadata"]["content_mapping_fixed"] = True
    data["metadata"]["content_fix_timestamp"] = datetime.now().isoformat()
    data["metadata"]["original_image_order_restored"] = True
    
    # 保存修复后的数据
    with open(parse_results_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ 内容映射修复完成！")
    print(f"📁 修复后文件: {parse_results_file}")
    
    # 验证修复结果
    print(f"\n🔍 验证修复结果:")
    for i in range(min(5, len(fixed_kdc_results))):
        result = fixed_kdc_results[i]
        expected_img = image_files[i] if i < len(image_files) else "N/A"
        actual_img = result.get("original_image", "N/A")
        match_status = "✅" if expected_img == actual_img else "❌"
        
        print(f"   {i+1}. {match_status} PDF: {result.get('filename', 'N/A')}")
        print(f"      期望图片: {expected_img}")
        print(f"      实际图片: {actual_img}")
    
    return True

if __name__ == "__main__":
    print("🔧 wi数据集内容映射关系修复工具")
    print("=" * 60)
    
    success = fix_wi_content_mapping()
    
    if success:
        print("\n🎉 wi数据集内容映射关系修复成功！")
        print("💡 现在analyzer应该能显示正确的文件与KDC解析结果对应关系")
        print("建议运行: DATASET_NAME=wi python generate_report.py")
    else:
        print("\n❌ 修复失败，请检查错误信息") 