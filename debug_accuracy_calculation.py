#!/usr/bin/env python3
"""
调试准确率计算问题
检查kingsoft数据集中博腾报告书_01.png的数据流程
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def load_annotation_data():
    """加载标注数据"""
    print("=== 加载标注数据 ===")
    
    annotation_file = project_root / "dataset/kingsoft/annotations/博腾报告书_01.json"
    
    if not annotation_file.exists():
        print(f"❌ 标注文件不存在: {annotation_file}")
        return None
    
    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            annotation_data = json.load(f)
        
        print(f"✅ 成功加载标注数据")
        print(f"数据结构: {type(annotation_data)}")
        
        if 'elements' in annotation_data:
            elements = annotation_data['elements']
            print(f"Elements数量: {len(elements)}")
            
            for i, element in enumerate(elements):
                if element.get('type') == 'table':
                    rows = element.get('rows', [])
                    print(f"表格 {i+1}: {len(rows)} 行")
                    
                    # 显示前几行数据
                    for j, row in enumerate(rows[:3]):
                        if isinstance(row, list):
                            row_content = [cell.get('content', '') for cell in row if isinstance(cell, dict)]
                            print(f"  行 {j+1}: {row_content}")
        
        return annotation_data
        
    except Exception as e:
        print(f"❌ 加载标注数据失败: {e}")
        return None

def load_parse_results():
    """加载解析结果"""
    print("\n=== 加载解析结果 ===")
    
    results_file = project_root / "parse_results/kingsoft/latest_results.json"
    
    if not results_file.exists():
        print(f"❌ 解析结果文件不存在: {results_file}")
        return None
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results_data = json.load(f)
        
        print(f"✅ 成功加载解析结果")
        
        # 检查解析结果结构
        parse_results = results_data.get('parse_results', {})
        print(f"解析器数量: {len(parse_results)}")
        
        for parser_name, parser_results in parse_results.items():
            print(f"  {parser_name}: {len(parser_results)} 个结果")
            
            if parser_results and len(parser_results) > 0:
                first_result = parser_results[0]
                if 'original_image' in first_result:
                    print(f"    图片: {first_result['original_image']}")
                if 'success' in first_result:
                    print(f"    成功: {first_result['success']}")
        
        return results_data
        
    except Exception as e:
        print(f"❌ 加载解析结果失败: {e}")
        return None

def extract_parse_result_for_image(results_data, image_name):
    """提取指定图片的解析结果"""
    print(f"\n=== 提取图片 {image_name} 的解析结果 ===")
    
    parse_results = results_data.get('parse_results', {})
    extracted_results = {}
    
    for parser_name, parser_results in parse_results.items():
        matching_result = None
        
        for result in parser_results:
            if result.get('original_image') == image_name:
                matching_result = result
                break
        
        if matching_result:
            print(f"✅ 找到 {parser_name} 的结果")
            extracted_results[parser_name] = matching_result
            
            # 显示结果摘要
            if 'result' in matching_result:
                result_data = matching_result['result']
                if isinstance(result_data, dict):
                    if 'data' in result_data and result_data['data']:
                        data_item = result_data['data'][0]
                        
                        # 显示不同类型的内容
                        if 'plain' in data_item:
                            plain_text = data_item['plain'][:200] + "..." if len(data_item['plain']) > 200 else data_item['plain']
                            print(f"    Plain文本: {plain_text}")
                        
                        if 'markdown' in data_item:
                            markdown_text = data_item['markdown'][:200] + "..." if len(data_item['markdown']) > 200 else data_item['markdown']
                            print(f"    Markdown: {markdown_text}")
        else:
            print(f"❌ 未找到 {parser_name} 的结果")
    
    return extracted_results

def simulate_accuracy_calculation(annotation_data, parse_results):
    """模拟准确率计算"""
    print("\n=== 模拟准确率计算 ===")
    
    # 从标注数据提取表格内容
    annotation_table = extract_table_from_annotation(annotation_data)
    if not annotation_table:
        print("❌ 无法从标注数据提取表格")
        return
    
    print(f"✅ 标注数据表格: {len(annotation_table)} 行")
    
    # 对每个解析器计算准确率
    for parser_name, result in parse_results.items():
        print(f"\n--- {parser_name} 准确率计算 ---")
        
        # 提取解析结果文本
        parsed_text = extract_text_from_parse_result(result, parser_name)
        if not parsed_text:
            print(f"❌ 无法提取 {parser_name} 的文本")
            continue
        
        print(f"解析文本长度: {len(parsed_text)}")
        print(f"解析文本预览: {parsed_text[:200]}...")
        
        # 简单的准确率计算（基于文本相似度）
        accuracy = calculate_simple_accuracy(annotation_table, parsed_text)
        print(f"简单准确率: {accuracy:.2f}%")

def extract_table_from_annotation(annotation_data):
    """从标注数据提取表格内容"""
    if not annotation_data or 'elements' not in annotation_data:
        return None
    
    for element in annotation_data['elements']:
        if element.get('type') == 'table':
            rows = element.get('rows', [])
            table_content = []
            
            for row in rows:
                if isinstance(row, list):
                    row_content = []
                    for cell in row:
                        if isinstance(cell, dict):
                            content = cell.get('content', '')
                            if isinstance(content, str):
                                row_content.append(content)
                            elif isinstance(content, dict):
                                # 处理复杂内容类型
                                if content.get('type') == 'checkbox':
                                    checkbox_text = f"{content.get('key', '')}: " + ", ".join([
                                        f"{'✓' if opt.get('checked') else '☐'} {opt.get('option', '')}"
                                        for opt in content.get('options', [])
                                    ])
                                    row_content.append(checkbox_text)
                                elif content.get('type') == 'list':
                                    list_text = "; ".join(content.get('items', []))
                                    row_content.append(list_text)
                                else:
                                    row_content.append(str(content))
                            else:
                                row_content.append(str(content))
                    table_content.append(row_content)
            
            return table_content
    
    return None

def extract_text_from_parse_result(result, parser_name):
    """从解析结果提取文本"""
    if not result or 'result' not in result:
        return ""
    
    result_data = result['result']
    
    if isinstance(result_data, dict) and 'data' in result_data and result_data['data']:
        data_item = result_data['data'][0]
        
        # 根据解析器类型提取相应的文本
        if parser_name == 'kdc_plain' and 'plain' in data_item:
            return data_item['plain']
        elif parser_name == 'kdc_markdown' and 'markdown' in data_item:
            return data_item['markdown']
        elif parser_name == 'kdc_kdc' and 'kdc' in data_item:
            return str(data_item['kdc'])  # KDC格式可能是复杂对象
        elif 'html' in result_data:
            return result_data['html']
        elif 'plain' in data_item:
            return data_item['plain']
    
    return ""

def calculate_simple_accuracy(annotation_table, parsed_text):
    """计算简单的准确率"""
    if not annotation_table or not parsed_text:
        return 0.0
    
    # 将标注表格转换为文本
    annotation_text = ""
    for row in annotation_table:
        annotation_text += " ".join(row) + " "
    
    annotation_text = annotation_text.strip().lower()
    parsed_text = parsed_text.strip().lower()
    
    # 计算字符级别的相似度
    if len(annotation_text) == 0:
        return 0.0
    
    # 简单的字符匹配计算
    common_chars = 0
    for char in annotation_text:
        if char in parsed_text:
            common_chars += 1
    
    accuracy = (common_chars / len(annotation_text)) * 100
    return min(accuracy, 100.0)  # 限制在100%以内

def main():
    """主函数"""
    print("开始调试kingsoft数据集的准确率计算问题")
    print("="*60)
    
    # 1. 加载标注数据
    annotation_data = load_annotation_data()
    if not annotation_data:
        return 1
    
    # 2. 加载解析结果
    results_data = load_parse_results()
    if not results_data:
        return 1
    
    # 3. 提取指定图片的解析结果
    image_name = "博腾报告书_01.png"
    parse_results = extract_parse_result_for_image(results_data, image_name)
    
    if not parse_results:
        print(f"❌ 未找到图片 {image_name} 的解析结果")
        return 1
    
    # 4. 模拟准确率计算
    simulate_accuracy_calculation(annotation_data, parse_results)
    
    print("\n" + "="*60)
    print("调试完成")
    
    # 5. 输出建议
    print("\n建议检查的问题:")
    print("1. 标注数据格式是否正确存储在数据库中")
    print("2. analyzer是否正确获取和解析标注数据")
    print("3. JSON Schema解析器是否正确处理复杂内容")
    print("4. 准确率计算算法是否适合当前数据格式")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
