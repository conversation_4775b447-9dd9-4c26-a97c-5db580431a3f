#!/usr/bin/env python3
"""
测试自动标注功能

使用方法:
1. 确保backend服务正在运行 (http://localhost:8000)
2. 运行: python test_auto_annotation.py
"""

import os
import sys
import requests
import json

# 添加parser/src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'parser/src'))

def test_table_parser():
    """测试表格解析器"""
    print("🧪 测试表格解析器...")
    
    try:
        from utils.table_parser import TableDataParser
        
        # 创建一个简单的HTML表格用于测试
        test_html = """
        <html>
        <head><meta charset="utf-8"></head>
        <body>
        <table>
            <tr><th>姓名</th><th>年龄</th><th>职位</th></tr>
            <tr><td>张三</td><td>25</td><td>工程师</td></tr>
            <tr><td>李四</td><td>30</td><td>设计师</td></tr>
        </table>
        </body>
        </html>
        """
        
        parser = TableDataParser()
        result = parser.parse_html_table(test_html)
        
        if result:
            print("✅ 表格解析成功")
            print(f"   结构: {result['table_structure'][:100]}...")
            print(f"   内容: {result['table_content'][:100]}...")
            return True
        else:
            print("❌ 表格解析失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入表格解析器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 表格解析器测试失败: {e}")
        return False

def test_backend_connection():
    """测试后端连接"""
    print("🔗 测试后端连接...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务连接成功")
            return True
        else:
            print(f"❌ 后端服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 后端连接测试失败: {e}")
        return False

def test_auto_annotation_service():
    """测试自动标注服务"""
    print("🏷️  测试自动标注服务...")
    
    try:
        from services.auto_annotation import AutoAnnotationService
        
        service = AutoAnnotationService("http://localhost:8000")
        print("✅ 自动标注服务创建成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入自动标注服务失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 自动标注服务测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("🌐 测试API端点...")
    
    base_url = "http://localhost:8000"
    
    # 测试数据集API
    try:
        response = requests.get(f"{base_url}/api/datasets", timeout=5)
        if response.status_code == 200:
            print("✅ 数据集API正常")
        else:
            print(f"⚠️  数据集API响应: {response.status_code}")
    except Exception as e:
        print(f"❌ 数据集API测试失败: {e}")
        return False
    
    # 测试标注API
    try:
        response = requests.get(f"{base_url}/api/annotations", timeout=5)
        if response.status_code == 200:
            print("✅ 标注API正常")
        else:
            print(f"⚠️  标注API响应: {response.status_code}")
    except Exception as e:
        print(f"❌ 标注API测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试自动标注功能\n")
    
    tests = [
        ("表格解析器", test_table_parser),
        ("后端连接", test_backend_connection),
        ("自动标注服务", test_auto_annotation_service),
        ("API端点", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*50}")
    
    print(f"\n🎯 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"总计: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("\n🎉 所有测试通过！自动标注功能已就绪。")
        print("\n📋 使用说明:")
        print("1. 启动backend服务: ./backend/start_server.sh")
        print("2. 生成表格数据: DATASET_NAME=test20 ./parser/table_parser.sh --num-tables=2")
        print("3. 运行自动标注: ./parser/table_parser.sh --mode=annotate")
        print("4. 启动analyzer查看结果: cd analyzer && npm start")
    else:
        print(f"\n⚠️  有 {len(tests) - passed} 个测试失败，请检查配置。")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
