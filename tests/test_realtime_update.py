#!/usr/bin/env python3
"""
测试实时更新latest_results.json功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加parser/src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "parser" / "src"))

from core.result_manager import Result<PERSON>ana<PERSON>

def test_realtime_update():
    """测试实时更新功能"""
    print("🔍 测试实时更新latest_results.json功能...")
    
    # 创建测试数据集
    test_dataset = "test_realtime"
    
    # 创建结果管理器
    result_manager = ResultManager(test_dataset)
    
    print(f"📁 测试数据集: {test_dataset}")
    print(f"📂 解析结果目录: {result_manager.parse_results_dir}")
    
    # 确保目录存在
    os.makedirs(result_manager.parse_results_dir, exist_ok=True)
    
    # 1. 初始化会话，检查是否立即创建了latest_results.json
    print("\n1️⃣ 初始化解析会话...")
    session_file = result_manager.init_session_results({
        "test_mode": True,
        "parser_config": {
            "enabled_parsers": ["kdc_markdown", "monkey_ocr"],
            "execution_mode": "parallel"
        }
    })
    
    latest_file = os.path.join(result_manager.parse_results_dir, "latest_results.json")
    if os.path.exists(latest_file):
        print("✅ latest_results.json 在初始化时已创建")
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"   状态: {data.get('metadata', {}).get('status')}")
        print(f"   进度: {data.get('progress', {}).get('completed_count')}/{data.get('progress', {}).get('total_count')}")
    else:
        print("❌ latest_results.json 在初始化时未创建")
        return False
    
    # 2. 模拟添加文件和更新进度
    print("\n2️⃣ 模拟解析过程...")
    
    test_files = [
        {"filename": "test1.png", "file_id": "file1"},
        {"filename": "test2.png", "file_id": "file2"},
        {"filename": "test3.png", "file_id": "file3"}
    ]
    
    result_manager.update_progress(total_count=len(test_files))
    result_manager.set_processed_files(test_files)
    
    # 检查进度更新后的latest_results.json
    with open(latest_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    print(f"   设置总数后 - 进度: {data.get('progress', {}).get('completed_count')}/{data.get('progress', {}).get('total_count')}")
    
    # 3. 逐个添加文件解析结果，检查实时更新
    for i, file_info in enumerate(test_files):
        print(f"\n3️⃣ 处理文件 {i+1}: {file_info['filename']}")
        
        # 模拟解析结果
        mock_results = {
            "kdc_markdown": {
                "success": True,
                "content": f"# 表格内容 {i+1}\n\n| 列1 | 列2 |\n|-----|-----|\n| 数据1 | 数据2 |",
                "metadata": {"parser": "kdc_markdown", "timestamp": "2025-06-27T00:00:00"},
                "filename": file_info["filename"],
                "file_id": file_info["file_id"],
                "original_image": file_info["filename"].replace('.png', '_original.png')
            },
            "monkey_ocr": {
                "success": True,
                "content": f"<table><tr><td>数据{i+1}</td></tr></table>",
                "metadata": {"parser": "monkey_ocr", "timestamp": "2025-06-27T00:00:00"},
                "filename": file_info["filename"],
                "file_id": file_info["file_id"],
                "original_image": file_info["filename"].replace('.png', '_original.png')
            }
        }
        
        # 添加解析结果
        result_manager.add_file_results(file_info, mock_results)
        
        # 立即检查latest_results.json是否更新
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        progress = data.get('progress', {})
        parse_results = data.get('parse_results', {})
        
        print(f"   ✅ 文件处理完成")
        print(f"   📊 当前进度: {progress.get('completed_count')}/{progress.get('total_count')}")
        print(f"   📝 解析结果数: kdc_markdown={len(parse_results.get('kdc_markdown', []))}, monkey_ocr={len(parse_results.get('monkey_ocr', []))}")
        print(f"   📈 状态: {data.get('metadata', {}).get('status')}")
        
        # 模拟一点延迟
        time.sleep(0.5)
    
    # 4. 完成会话
    print("\n4️⃣ 完成解析会话...")
    final_result_file = result_manager.finalize_session()
    
    # 检查最终的latest_results.json
    with open(latest_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"✅ 解析会话完成")
    print(f"📊 最终状态: {data.get('metadata', {}).get('status')}")
    print(f"📈 最终进度: {data.get('progress', {}).get('completed_count')}/{data.get('progress', {}).get('total_count')}")
    
    return True

def cleanup_test_data():
    """清理测试数据"""
    test_dataset = "test_realtime"
    test_dir = f"parse_results/{test_dataset}"
    
    if os.path.exists(test_dir):
        import shutil
        shutil.rmtree(test_dir)
        print(f"🧹 已清理测试数据: {test_dir}")

if __name__ == "__main__":
    print("🚀 开始测试实时更新latest_results.json功能...")
    
    try:
        success = test_realtime_update()
        
        print("\n" + "="*60)
        if success:
            print("🎉 实时更新功能测试通过！")
            print("✅ latest_results.json 现在会在每次解析结果更新时实时更新")
            print("✅ 前端可以通过轮询latest_results.json获取实时进度")
        else:
            print("❌ 实时更新功能测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        cleanup_test_data()
