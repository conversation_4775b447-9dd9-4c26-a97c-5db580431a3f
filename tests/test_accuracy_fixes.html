<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Accuracy Fixes</title>
</head>
<body>
    <h1>Test Accuracy Fixes</h1>
    
    <div id="results"></div>

    <script>
        // Copy the fixed accuracy calculation functions
        const removeTrailingEmptyColumns = (table) => {
            if (!table || table.length === 0) return table;

            // 找到最大列数
            const maxCols = Math.max(...table.map(row => row.length));
            
            // 从右到左检查每一列是否为空
            let lastNonEmptyCol = -1;
            for (let col = 0; col < maxCols; col++) {
                let hasContent = false;
                for (let row = 0; row < table.length; row++) {
                    const cell = (table[row][col] || '').trim();
                    if (cell !== '') {
                        hasContent = true;
                        break;
                    }
                }
                if (hasContent) {
                    lastNonEmptyCol = col;
                }
            }

            // 如果所有列都为空，返回原表格
            if (lastNonEmptyCol === -1) return table;

            // 移除尾部空列
            return table.map(row => row.slice(0, lastNonEmptyCol + 1));
        };

        const compareTableData = (table1, table2) => {
            if (!table1 || !table2) return 0;

            // 检查行数
            if (table1.length !== table2.length) {
                return Math.max(0, 100 - Math.abs(table1.length - table2.length) * 10);
            }

            // 移除尾部的空列
            const cleanTable1 = removeTrailingEmptyColumns(table1);
            const cleanTable2 = removeTrailingEmptyColumns(table2);

            let totalCells = 0;
            let matchingCells = 0;

            for (let i = 0; i < cleanTable1.length; i++) {
                const row1 = cleanTable1[i] || [];
                const row2 = cleanTable2[i] || [];
                const maxCols = Math.max(row1.length, row2.length);

                for (let j = 0; j < maxCols; j++) {
                    totalCells++;
                    const cell1 = (row1[j] || '').toLowerCase().trim();
                    const cell2 = (row2[j] || '').toLowerCase().trim();

                    // 对于中文括号的兼容性处理
                    const normalizedCell1 = cell1.replace(/（/g, '(').replace(/）/g, ')');
                    const normalizedCell2 = cell2.replace(/（/g, '(').replace(/）/g, ')');

                    if (normalizedCell1 === normalizedCell2) {
                        matchingCells++;
                    }
                }
            }

            return totalCells > 0 ? (matchingCells / totalCells) * 100 : 0;
        };

        const parseTableContent = (content) => {
            try {
                // 尝试解析HTML表格
                if (content.includes('<table')) {
                    return parseHTMLTable(content);
                }
                
                // 尝试解析Markdown表格
                if (content.includes('|')) {
                    return parseMarkdownTable(content);
                }
                
                return null;
            } catch (error) {
                return null;
            }
        };

        const parseHTMLTable = (htmlContent) => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');
            const table = doc.querySelector('table');
            
            if (!table) return null;
            
            const rows = [];
            const trs = table.querySelectorAll('tr');
            
            trs.forEach(tr => {
                const cells = [];
                const tds = tr.querySelectorAll('th, td');
                tds.forEach(td => {
                    cells.push(td.textContent.trim());
                });
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });
            
            return rows;
        };

        const parseMarkdownTable = (markdownContent) => {
            const lines = markdownContent.split('\n').map(line => line.trim()).filter(line => line);
            const tableLines = lines.filter(line => line.startsWith('|') && line.endsWith('|'));

            if (tableLines.length < 2) return null;

            // 移除分隔符行
            const dataLines = tableLines.filter(line => !line.match(/^\|[\s\-|]+\|$/));

            const rows = [];
            dataLines.forEach(line => {
                const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });

            return rows;
        };

        const calculateAccuracy = (expected, actual) => {
            if (!expected || !actual) return 0;

            // 检查是否为解析失败的结果
            const actualStr = String(actual);
            if (actualStr.includes('MonkeyOCR处理失败') || 
                actualStr.includes('MonkeyOCR文件上传失败') ||
                actualStr.includes('MonkeyOCR处理超时') ||
                actualStr.includes('MonkeyOCR获取结果失败') ||
                actualStr.includes('MonkeyOCR处理请求提交失败') ||
                actualStr.includes('VL LLM处理失败') ||
                actualStr.includes('KDC处理失败')) {
                return 0;
            }

            try {
                // 尝试解析表格内容进行比较
                const expectedTable = parseTableContent(String(expected));
                const actualTable = parseTableContent(String(actual));

                if (expectedTable && actualTable) {
                    return compareTableData(expectedTable, actualTable);
                } else {
                    // 如果无法解析为表格，使用简单字符串比较
                    return 0; // 简化处理
                }
            } catch (error) {
                console.warn('表格解析失败:', error);
                return 0;
            }
        };

        // Test cases
        const tests = [
            {
                name: "MonkeyOCR Failed Result",
                expected: "<table><tr><th>项目</th><th>收入</th></tr><tr><td>A</td><td>100</td></tr></table>",
                actual: "MonkeyOCR文件上传失败",
                expectedAccuracy: 0
            },
            {
                name: "Perfect Match with Different Brackets",
                expected: "<table><tr><th>项目</th><th>营业收入（万元）</th></tr><tr><td>主营业务收入</td><td>12000</td></tr></table>",
                actual: "| 项目 | 营业收入(万元) |\n| --- | --- |\n| 主营业务收入 | 12000 |",
                expectedAccuracy: 100
            },
            {
                name: "Extra Empty Column",
                expected: "<table><tr><th>项目</th><th>收入</th></tr><tr><td>A</td><td>100</td></tr></table>",
                actual: "| 项目 | 收入 |  |\n| --- | --- | --- |\n| A | 100 |  |",
                expectedAccuracy: 100
            }
        ];

        // Run tests
        let results = "<h2>Test Results:</h2>";
        tests.forEach((test, index) => {
            const accuracy = calculateAccuracy(test.expected, test.actual);
            const passed = Math.abs(accuracy - test.expectedAccuracy) < 0.1;
            
            results += `
                <div style="margin: 10px 0; padding: 10px; border: 1px solid ${passed ? 'green' : 'red'};">
                    <h3>Test ${index + 1}: ${test.name}</h3>
                    <p><strong>Expected Accuracy:</strong> ${test.expectedAccuracy}%</p>
                    <p><strong>Actual Accuracy:</strong> ${accuracy.toFixed(1)}%</p>
                    <p><strong>Result:</strong> <span style="color: ${passed ? 'green' : 'red'};">${passed ? 'PASS' : 'FAIL'}</span></p>
                </div>
            `;
        });

        document.getElementById('results').innerHTML = results;
    </script>
</body>
</html>
