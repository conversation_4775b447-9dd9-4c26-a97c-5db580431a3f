#!/usr/bin/env python3
"""
测试latest_results.json创建的脚本
验证解析完成后是否正确创建latest_results.json文件
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# 添加parser/src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "parser" / "src"))

from core.result_manager import ResultManager

def test_latest_results_creation():
    """测试latest_results.json的创建"""
    print("🔍 测试latest_results.json创建...")
    
    # 创建临时测试数据集
    test_dataset = "test_latest_results"
    
    # 创建结果管理器
    result_manager = ResultManager(test_dataset)
    
    print(f"📁 测试数据集: {test_dataset}")
    print(f"📂 解析结果目录: {result_manager.parse_results_dir}")
    
    # 确保目录存在
    os.makedirs(result_manager.parse_results_dir, exist_ok=True)
    
    # 1. 测试初始化会话
    print("\n1️⃣ 初始化解析会话...")
    session_file = result_manager.init_session_results({
        "test_mode": True,
        "parser_config": {
            "enabled_parsers": ["kdc_markdown", "monkey_ocr"],
            "execution_mode": "parallel"
        }
    })
    print(f"✅ 会话文件创建: {session_file}")
    
    # 2. 模拟添加一些解析结果
    print("\n2️⃣ 模拟添加解析结果...")
    
    # 模拟处理文件
    test_files = [
        {"filename": "test1.png", "file_id": "file1", "url": "http://example.com/test1.png"},
        {"filename": "test2.png", "file_id": "file2", "url": "http://example.com/test2.png"}
    ]
    
    result_manager.update_progress(total_count=len(test_files))
    result_manager.set_processed_files(test_files)

    for i, file_info in enumerate(test_files):
        filename = file_info["filename"]
        file_id = file_info["file_id"]
        
        print(f"   处理文件: {filename}")
        
        # 更新进度
        result_manager.update_progress(
            current_file=filename,
            completed_count=i
        )
        
        # 模拟解析结果
        mock_results = {
            "kdc_markdown": {
                "success": True,
                "content": f"# 表格内容 {i+1}\n\n| 列1 | 列2 |\n|-----|-----|\n| 数据1 | 数据2 |",
                "metadata": {"parser": "kdc_markdown", "timestamp": "2025-06-27T00:00:00"},
                "filename": filename,
                "file_id": file_id,
                "original_image": filename.replace('.png', '_original.png')
            },
            "monkey_ocr": {
                "success": True,
                "content": f"<table><tr><td>数据{i+1}</td></tr></table>",
                "metadata": {"parser": "monkey_ocr", "timestamp": "2025-06-27T00:00:00"},
                "filename": filename,
                "file_id": file_id,
                "original_image": filename.replace('.png', '_original.png')
            }
        }

        # 保存解析结果
        result_manager.add_file_results(file_info, mock_results)
    
    # 3. 完成会话
    print("\n3️⃣ 完成解析会话...")
    final_result_file = result_manager.finalize_session()
    print(f"✅ 最终结果文件: {final_result_file}")
    
    # 4. 检查latest_results.json是否创建
    print("\n4️⃣ 检查latest_results.json...")
    latest_file = os.path.join(result_manager.parse_results_dir, "latest_results.json")
    
    if os.path.exists(latest_file):
        print(f"✅ latest_results.json 已创建: {latest_file}")
        
        # 读取并验证内容
        with open(latest_file, 'r', encoding='utf-8') as f:
            latest_data = json.load(f)
        
        print(f"📊 数据集名称: {latest_data.get('dataset_name')}")
        print(f"📅 解析时间: {latest_data.get('timestamp')}")
        print(f"📈 状态: {latest_data.get('metadata', {}).get('status')}")
        print(f"📁 处理文件数: {len(latest_data.get('processed_files', []))}")
        print(f"🔍 解析结果数: {len(latest_data.get('parse_results', {}))}")
        
        # 验证解析结果
        parse_results = latest_data.get('parse_results', {})
        for parser_name, results in parse_results.items():
            if isinstance(results, list):
                print(f"   解析器 {parser_name}: {len(results)} 个结果")
            else:
                print(f"   解析器 {parser_name}: {type(results)}")
        
        return True
    else:
        print(f"❌ latest_results.json 未创建: {latest_file}")
        return False

def test_latest_results_from_existing():
    """测试从现有结果文件创建latest_results.json"""
    print("\n🔧 测试从现有结果文件创建latest_results.json...")
    
    # 使用kingsoft数据集进行测试
    test_dataset = "kingsoft"
    result_manager = ResultManager(test_dataset)
    
    print(f"📁 测试数据集: {test_dataset}")
    print(f"📂 解析结果目录: {result_manager.parse_results_dir}")
    
    # 检查是否有现有的解析结果文件
    available_results = result_manager.list_available_results()
    if available_results:
        print(f"📋 找到 {len(available_results)} 个现有结果文件")
        
        # 备份当前的latest_results.json（如果存在）
        latest_file = os.path.join(result_manager.parse_results_dir, "latest_results.json")
        backup_file = None
        if os.path.exists(latest_file):
            backup_file = f"{latest_file}.test_backup"
            shutil.copy2(latest_file, backup_file)
            print(f"📦 已备份现有latest_results.json到: {backup_file}")
        
        # 删除latest_results.json
        if os.path.exists(latest_file):
            os.remove(latest_file)
            print(f"🗑️ 已删除latest_results.json")
        
        # 从最新结果文件创建latest_results.json
        created_file = result_manager.create_latest_results_from_most_recent()
        
        if created_file:
            print(f"✅ 成功创建latest_results.json: {created_file}")
            
            # 验证创建的文件
            with open(created_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📊 数据集名称: {data.get('dataset_name')}")
            print(f"📅 解析时间: {data.get('timestamp')}")
            print(f"📈 状态: {data.get('metadata', {}).get('status')}")
            
            # 恢复备份（如果有）
            if backup_file and os.path.exists(backup_file):
                shutil.move(backup_file, latest_file)
                print(f"🔄 已恢复原始latest_results.json")
            
            return True
        else:
            print(f"❌ 创建latest_results.json失败")
            
            # 恢复备份（如果有）
            if backup_file and os.path.exists(backup_file):
                shutil.move(backup_file, latest_file)
                print(f"🔄 已恢复原始latest_results.json")
            
            return False
    else:
        print(f"⚠️ 没有找到现有的解析结果文件")
        return False

def cleanup_test_data():
    """清理测试数据"""
    test_dataset = "test_latest_results"
    test_dir = f"parse_results/{test_dataset}"
    
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print(f"🧹 已清理测试数据: {test_dir}")

if __name__ == "__main__":
    print("🚀 开始测试latest_results.json创建功能...")
    
    try:
        # 测试1：完整的解析流程
        success1 = test_latest_results_creation()
        
        # 测试2：从现有文件创建
        success2 = test_latest_results_from_existing()
        
        print("\n" + "="*60)
        print("📋 测试结果总结:")
        print(f"   完整解析流程测试: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"   从现有文件创建测试: {'✅ 通过' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print("🎉 所有测试通过！latest_results.json创建功能正常")
        else:
            print("⚠️ 部分测试失败，请检查相关功能")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        cleanup_test_data()
