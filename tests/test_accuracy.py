#!/usr/bin/env python3
"""
测试准确率计算功能

验证修复后的准确率计算是否正确工作
"""

import sys
import os
import json
import requests

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_accuracy_calculation():
    """测试准确率计算"""
    print("🧪 测试准确率计算...")
    
    try:
        from services.accuracy import AccuracyCalculator
        
        # 创建准确率计算器
        calculator = AccuracyCalculator()
        
        # 模拟标注数据（HTML格式）
        ground_truth = {
            'table_structure': json.dumps({
                'rows': 5,
                'cols': 6,
                'headers': ['项目', '营业收入（万元）', '营业成本（万元）', '毛利润（万元）', '毛利率（%）', '净利润（万元）'],
                'data_types': ['string', 'number', 'number', 'number', 'number', 'number'],
                'has_header': True,
                'merged_cells': [],
                'table_type': 'data_table'
            }),
            'table_content': '''<table>
  <thead>
    <tr>
      <th>项目</th>
      <th>营业收入（万元）</th>
      <th>营业成本（万元）</th>
      <th>毛利润（万元）</th>
      <th>毛利率（%）</th>
      <th>净利润（万元）</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>主营业务收入</td>
      <td>12000</td>
      <td>8400</td>
      <td>3600</td>
      <td>30</td>
      <td>2100</td>
    </tr>
    <tr>
      <td>投资收益</td>
      <td>500</td>
      <td>200</td>
      <td>300</td>
      <td>60</td>
      <td>180</td>
    </tr>
    <tr>
      <td>其他业务收入</td>
      <td>800</td>
      <td>400</td>
      <td>400</td>
      <td>50</td>
      <td>280</td>
    </tr>
    <tr>
      <td>营业费用</td>
      <td>1000</td>
      <td>800</td>
      <td>200</td>
      <td>20</td>
      <td>150</td>
    </tr>
  </tbody>
</table>'''
        }
        
        # 使用实际的VL LLM解析结果（与标注数据完全匹配）
        vl_llm_prediction = {
            'parser_name': 'vl_llm',
            'result': {
                'content': {
                    'choices': [{
                        'message': {
                            'content': "| 项目         | 营业收入（万元） | 营业成本（万元） | 毛利润（万元） | 毛利率（%） | 净利润（万元） |\n|--------------|------------------|------------------|----------------|--------------|-----------------|\n| 主营业务收入 | 12000            | 8400             | 3600           | 30           | 2100            |\n| 投资收益     | 500              | 200              | 300            | 60           | 180             |\n| 其他业务收入 | 800              | 400              | 400            | 50           | 280             |\n| 营业费用     | 1000             | 800              | 200            | 20           | 150             |\n"
                        }
                    }]
                }
            }
        }
        
        # 计算准确率
        result = calculator.calculate_accuracy(ground_truth, vl_llm_prediction)
        
        print("✅ 准确率计算成功")
        print(f"   结构准确率: {result['structure_accuracy']}")
        print(f"   内容准确率: {result['content_accuracy']}")
        print(f"   综合准确率: {result['overall_accuracy']}")
        
        # 验证结果合理性
        if result['content_accuracy'] > 0.8:
            print("✅ 内容准确率正常（>0.8）")
        else:
            print(f"⚠️  内容准确率偏低: {result['content_accuracy']}")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 准确率计算测试失败: {e}")
        return False

def test_api_evaluation():
    """测试API评估功能"""
    print("\n🌐 测试API评估功能...")
    
    try:
        # 获取标注数据
        response = requests.get("http://localhost:8000/api/annotations?dataset_name=test20")
        if response.status_code != 200:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return False
            
        annotations = response.json()
        if not annotations:
            print("❌ 没有标注数据")
            return False
            
        annotation = annotations[0]
        print(f"✅ 获取到标注数据: {annotation['id']}")
        
        # 模拟解析结果
        parser_results = {
            'parser_name': 'vl_llm',
            'result': {
                'content': {
                    'choices': [{
                        'message': {
                            'content': '''| 产品名称   | 销售数量 | 销售金额（元） | 销售额占比（%） |
|------------|----------|----------------|------------------|
| 智能手机   | 500      | 250000         | 40               |
| 笔记本电脑 | 300      | 180000         | 30               |
| 平板电脑   | 200      | 100000         | 20               |
| 智能手表   | 400      | 160000         | 25               |
| 游戏主机   | 100      | 80000          | 12               |
| 平板电视   | 250      | 150000         | 24               |
| 耳机       | 600      | 72000          | 12               |
| 智能音箱   | 800      | 48000          | 8                |'''
                        }
                    }]
                }
            }
        }
        
        # 调用评估API
        eval_request = {
            'image_id': annotation['image_id'],
            'annotation_id': annotation['id'],
            'parser_results': parser_results
        }
        
        response = requests.post("http://localhost:8000/api/evaluate", json=eval_request)
        if response.status_code == 200:
            result = response.json()
            print("✅ API评估成功")
            print(f"   结构准确率: {result['structure_accuracy']}")
            print(f"   内容准确率: {result['content_accuracy']}")
            print(f"   综合准确率: {result['overall_accuracy']}")
            return True
        else:
            print(f"❌ API评估失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API评估测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试准确率计算功能\n")
    
    tests = [
        ("准确率计算器", test_accuracy_calculation),
        ("API评估功能", test_api_evaluation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*50}")
    
    print(f"\n🎯 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"总计: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("\n🎉 所有测试通过！准确率计算功能正常。")
    else:
        print(f"\n⚠️  有 {len(tests) - passed} 个测试失败，请检查配置。")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
