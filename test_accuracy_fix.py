#!/usr/bin/env python3
"""
测试准确率计算修复
验证JSON Schema标注数据的准确率计算是否正确
"""

import os
import sys
import json
import requests
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_annotation_api():
    """测试标注数据API"""
    print("=== 测试标注数据API ===")
    
    try:
        # 测试获取kingsoft数据集的标注数据
        response = requests.get(
            "http://localhost:8000/api/annotations",
            params={"dataset_name": "kingsoft"},
            timeout=10
        )
        
        if response.status_code == 200:
            annotations = response.json()
            print(f"✅ 成功获取标注数据，数量: {len(annotations)}")
            
            for ann in annotations:
                if ann['image_filename'] == '博腾报告书_01.png':
                    print(f"找到目标图片的标注:")
                    print(f"  ID: {ann['id']}")
                    print(f"  类型: {ann['annotation_type']}")
                    print(f"  标注员: {ann['annotator']}")
                    print(f"  状态: {ann['status']}")
                    
                    # 检查table_content
                    try:
                        content = json.loads(ann['table_content'])
                        print(f"  ✅ table_content是有效的JSON")
                        if 'elements' in content:
                            print(f"  包含elements: {len(content['elements'])}")
                        return ann
                    except:
                        print(f"  ❌ table_content不是有效的JSON")
                        return None
            
            print("❌ 未找到博腾报告书_01.png的标注")
            return None
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return None

def test_parse_results_api():
    """测试解析结果API"""
    print("\n=== 测试解析结果API ===")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/datasets/kingsoft/parse_results",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取解析结果")
            
            parse_results = data.get('parse_results', {})
            print(f"解析器数量: {len(parse_results)}")
            
            # 查找博腾报告书_01.png的结果
            target_results = {}
            for parser_name, results in parse_results.items():
                for result in results:
                    if result.get('original_image') == '博腾报告书_01.png':
                        target_results[parser_name] = result
                        break
            
            print(f"找到目标图片的解析结果: {list(target_results.keys())}")
            return target_results
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return None

def simulate_frontend_accuracy_calculation(annotation_data, parse_results):
    """模拟前端的准确率计算"""
    print("\n=== 模拟前端准确率计算 ===")
    
    if not annotation_data or not parse_results:
        print("❌ 缺少必要的数据")
        return
    
    # 模拟前端的数据处理逻辑
    try:
        # 解析标注数据
        table_content = json.loads(annotation_data['table_content'])
        print(f"✅ 成功解析标注数据")
        
        # 检查是否为JSON Schema格式
        if 'elements' in table_content:
            print(f"✅ 标注数据是JSON Schema格式")
            
            # 提取表格内容
            for element in table_content['elements']:
                if element.get('type') == 'table':
                    rows = element.get('rows', [])
                    print(f"标注表格: {len(rows)} 行")
                    
                    # 转换为文本用于比较
                    annotation_text = extract_text_from_json_schema_table(rows)
                    print(f"标注文本长度: {len(annotation_text)}")
                    print(f"标注文本预览: {annotation_text[:200]}...")
                    
                    # 与各个解析器结果比较
                    for parser_name, result in parse_results.items():
                        print(f"\n--- {parser_name} ---")
                        
                        # 提取解析结果文本
                        parsed_text = extract_text_from_parse_result(result, parser_name)
                        if parsed_text:
                            print(f"解析文本长度: {len(parsed_text)}")
                            
                            # 计算简单准确率
                            accuracy = calculate_text_similarity(annotation_text, parsed_text)
                            print(f"文本相似度: {accuracy:.2f}%")
                        else:
                            print("❌ 无法提取解析文本")
                    
                    break
        else:
            print("❌ 标注数据不是JSON Schema格式")
            
    except Exception as e:
        print(f"❌ 模拟计算失败: {e}")

def extract_text_from_json_schema_table(rows):
    """从JSON Schema表格提取文本"""
    text_parts = []
    
    for row in rows:
        if isinstance(row, list):
            row_text = []
            for cell in row:
                if isinstance(cell, dict):
                    content = cell.get('content', '')
                    if isinstance(content, str):
                        row_text.append(content)
                    elif isinstance(content, dict):
                        # 处理复杂内容
                        if content.get('type') == 'checkbox':
                            checkbox_text = content.get('key', '') + ': '
                            options = content.get('options', [])
                            checkbox_text += ', '.join([
                                f"{'✓' if opt.get('checked') else '☐'} {opt.get('option', '')}"
                                for opt in options
                            ])
                            row_text.append(checkbox_text)
                        elif content.get('type') == 'list':
                            list_text = '; '.join(content.get('items', []))
                            row_text.append(list_text)
                        else:
                            row_text.append(str(content))
                    else:
                        row_text.append(str(content))
            text_parts.append(' '.join(row_text))
    
    return ' '.join(text_parts)

def extract_text_from_parse_result(result, parser_name):
    """从解析结果提取文本"""
    if not result or 'result' not in result:
        return ""
    
    result_data = result['result']
    
    if isinstance(result_data, dict) and 'data' in result_data and result_data['data']:
        data_item = result_data['data'][0]
        
        if parser_name == 'kdc_plain' and 'plain' in data_item:
            return data_item['plain']
        elif parser_name == 'kdc_markdown' and 'markdown' in data_item:
            return data_item['markdown']
        elif 'plain' in data_item:
            return data_item['plain']
    
    if 'html' in result_data:
        return result_data['html']
    
    return ""

def calculate_text_similarity(text1, text2):
    """计算文本相似度"""
    if not text1 or not text2:
        return 0.0
    
    # 简单的字符级相似度计算
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()
    
    # 计算共同字符数
    common_chars = 0
    text2_chars = list(text2)
    
    for char in text1:
        if char in text2_chars:
            text2_chars.remove(char)
            common_chars += 1
    
    # 计算相似度
    max_length = max(len(text1), len(text2))
    if max_length == 0:
        return 100.0
    
    similarity = (common_chars / max_length) * 100
    return min(similarity, 100.0)

def main():
    """主函数"""
    print("开始测试准确率计算修复")
    print("="*50)
    
    # 1. 测试标注数据API
    annotation_data = test_annotation_api()
    
    # 2. 测试解析结果API
    parse_results = test_parse_results_api()
    
    # 3. 模拟前端准确率计算
    simulate_frontend_accuracy_calculation(annotation_data, parse_results)
    
    print("\n" + "="*50)
    print("测试完成")
    
    if annotation_data and parse_results:
        print("✅ 数据获取正常，准确率计算逻辑已修复")
        print("\n建议:")
        print("1. 重启analyzer前端服务")
        print("2. 在浏览器中测试kingsoft数据集")
        print("3. 检查准确率计算是否正确显示")
        return 0
    else:
        print("❌ 数据获取失败，请检查:")
        print("1. 后端服务是否正常运行")
        print("2. 数据库连接是否正常")
        print("3. 标注数据是否正确存储")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
