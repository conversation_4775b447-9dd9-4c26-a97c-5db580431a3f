# TableRAG 人工标注系统使用示例

本文档提供了TableRAG人工标注系统的详细使用示例，帮助用户快速上手。

## 🚀 系统启动

### 1. 一键启动
```bash
./start_tablerag.sh
```

启动成功后，您将看到：
```
TableRAG 系统启动完成！
=========================
后端服务: http://localhost:8000
  - API文档: http://localhost:8000/docs
  - 健康检查: http://localhost:8000/health

前端服务: http://localhost:3000
  - 解析分析: 查看多路OCR解析结果
  - 人工标注: 创建和管理标注数据
```

## 📊 功能演示

### 1. 数据集管理

**查看数据集列表：**
```bash
curl http://localhost:8000/api/datasets
```

**创建新数据集：**
```bash
curl -X POST http://localhost:8000/api/datasets \
  -H "Content-Type: application/json" \
  -d '{"name": "test_annotation", "description": "测试标注数据集"}'
```

### 2. 人工标注流程

#### 步骤1：选择数据集和图片
1. 打开前端界面：http://localhost:3000
2. 在数据集选择器中选择一个数据集（如"kingsoft"）
3. 在左侧案例列表中选择一张图片

#### 步骤2：切换到标注模式
1. 点击页面顶部的"人工标注"标签
2. 确认选中的图片显示在标注面板中

#### 步骤3：创建标注
1. 点击"新建标注"按钮
2. 填写标注信息：
   - **标注员**：输入您的姓名
   - **表格结构**：设置行数和列数（如：3行2列）
   - **表格内容**：输入标准答案

**示例表格内容（Markdown格式）：**
```markdown
| 项目 | 数值 |
| --- | --- |
| 收入 | 1000万 |
| 支出 | 800万 |
| 利润 | 200万 |
```

3. 点击"保存"完成标注

#### 步骤4：管理标注
- 在标注列表中查看已创建的标注
- 点击"编辑"修改标注内容
- 点击"删除"移除不需要的标注

### 3. 准确率评估

#### API方式评估
```bash
# 评估单个图片的解析结果
curl -X POST http://localhost:8000/api/evaluate \
  -H "Content-Type: application/json" \
  -d '{
    "image_id": 1,
    "annotation_id": 1,
    "parser_results": {
      "parser_name": "kdc_markdown",
      "result": "| 项目 | 数值 |\n| --- | --- |\n| 收入 | 1000万 |\n| 支出 | 800万 |\n| 利润 | 200万 |",
      "table_structure": "{\"rows\": 3, \"cols\": 2}"
    }
  }'
```

#### 查看评估结果
```bash
# 获取数据集的评估摘要
curl http://localhost:8000/api/evaluations/kingsoft/summary
```

### 4. 报告生成

#### 生成准确率报告
```bash
curl -X POST http://localhost:8000/api/reports/generate \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_name": "kingsoft",
    "report_type": "accuracy",
    "title": "Kingsoft数据集准确率报告",
    "created_by": "测试用户"
  }'
```

#### 查看报告列表
```bash
curl http://localhost:8000/api/reports?dataset_name=kingsoft
```

#### 获取报告内容
```bash
curl http://localhost:8000/api/reports/1
```

## 🔧 高级用法

### 1. 批量标注

对于大量图片，可以通过API批量创建标注：

```python
import requests

# 批量标注示例
annotations = [
    {
        "image_id": 1,
        "annotator": "标注员A",
        "table_structure": '{"rows": 3, "cols": 2}',
        "table_content": "| 项目 | 数值 |\n| --- | --- |\n| 收入 | 1000万 |",
        "status": "completed"
    },
    # 更多标注...
]

for annotation in annotations:
    response = requests.post(
        "http://localhost:8000/api/annotations",
        json=annotation
    )
    print(f"创建标注: {response.json()}")
```

### 2. 自定义准确率计算

可以扩展 `backend/services/accuracy.py` 来实现自定义的准确率计算算法：

```python
class CustomAccuracyCalculator(AccuracyCalculator):
    def calculate_custom_metric(self, ground_truth, prediction):
        # 实现自定义评估指标
        pass
```

### 3. 数据导入导出

**导出标注数据：**
```bash
# 获取所有标注数据
curl http://localhost:8000/api/annotations > annotations.json
```

**导入标注数据：**
```python
import json
import requests

with open('annotations.json', 'r') as f:
    annotations = json.load(f)

for annotation in annotations:
    # 移除ID字段，让系统自动分配
    annotation.pop('id', None)
    annotation.pop('created_at', None)
    annotation.pop('updated_at', None)
    
    response = requests.post(
        "http://localhost:8000/api/annotations",
        json=annotation
    )
```

## 🐛 故障排除

### 1. 后端服务无法启动
```bash
# 检查端口占用
lsof -i :8000

# 查看错误日志
tail -f backend/backend.log
```

### 2. 前端无法连接后端
```bash
# 检查后端服务状态
curl http://localhost:8000/health

# 检查CORS配置
# 确保后端允许前端域名访问
```

### 3. 数据库连接问题
```bash
# 检查数据库配置
echo $DATABASE_URL

# 测试数据库连接
python -c "
from backend.database.database import engine
try:
    engine.connect()
    print('数据库连接成功')
except Exception as e:
    print(f'数据库连接失败: {e}')
"
```

### 4. 图片无法显示
```bash
# 检查图片文件权限
ls -la dataset/kingsoft/images/

# 检查静态文件服务
curl -I http://localhost:8000/static/dataset/kingsoft/images/example.png
```

## 📈 性能优化建议

### 1. 数据库优化
- 使用MySQL而不是SQLite以获得更好的性能
- 为经常查询的字段添加索引
- 定期清理过期的评估结果

### 2. 前端优化
- 启用图片懒加载
- 使用虚拟滚动处理大量数据
- 缓存API响应结果

### 3. 后端优化
- 使用连接池管理数据库连接
- 启用API响应缓存
- 使用异步处理批量操作

## 🔗 相关资源

- [FastAPI文档](https://fastapi.tiangolo.com/)
- [React文档](https://reactjs.org/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)
- [MySQL文档](https://dev.mysql.com/doc/)

## 💡 最佳实践

1. **标注质量**：确保标注数据的准确性和一致性
2. **版本管理**：对重要的标注数据进行版本控制
3. **备份策略**：定期备份数据库和标注数据
4. **团队协作**：建立标注规范和质量检查流程
5. **持续改进**：根据评估结果不断优化解析算法
