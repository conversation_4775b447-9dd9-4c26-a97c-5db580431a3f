# JSON Schema 表格数据集成指南

本指南说明如何使用新的JSON Schema格式来处理表格数据，包括生成、标注和准确率计算。

## 概述

系统已升级支持JSON Schema格式的表格数据，主要改进包括：

1. **标注数据格式**：数据库中的标注数据现在使用JSON Schema格式存储
2. **生成流程**：Parser生成JSON Schema格式的表格数据，而不是传统的Markdown
3. **准确率计算**：基于JSON Schema数据进行更精确的准确率计算
4. **复杂内容支持**：支持复选框、列表等复杂表格内容

## JSON Schema 数据格式

### 基本结构

```json
{
  "elements": [
    {
      "type": "table",
      "rows": [
        [
          {"content": "表头1"},
          {"content": "表头2"},
          {"content": "表头3"}
        ],
        [
          {"content": "数据1"},
          {"content": "数据2"},
          {"content": "数据3"}
        ]
      ]
    }
  ],
  "industry": "相关行业"
}
```

### 支持的内容类型

#### 1. 普通文本
```json
{"content": "普通文本内容"}
```

#### 2. 复选框
```json
{
  "content": {
    "type": "checkbox",
    "key": "选项组名称",
    "options": [
      {"option": "选项1", "checked": true},
      {"option": "选项2", "checked": false}
    ]
  }
}
```

#### 3. 列表
```json
{
  "content": {
    "type": "list",
    "items": ["项目1", "项目2", "项目3"]
  }
}
```

#### 4. 合并单元格
```json
{
  "content": "合并单元格内容",
  "colspan": 2,
  "rowspan": 1
}
```

## 使用流程

### 1. 数据库迁移

首先运行数据库迁移脚本：

```bash
python backend/migrations/add_json_schema_support.py
```

### 2. 生成JSON Schema数据

使用更新后的Parser生成数据：

```python
from parser.src.tasks.data_generator import DataGenerator

generator = DataGenerator()
generator.gen_data_dir = "path/to/gen_data"
generator.images_dir = "path/to/images"

# 生成JSON Schema格式的表格数据
generator.generate_tables(num_tables=10, use_llm=True)
```

### 3. 创建标注数据

通过API创建JSON Schema格式的标注：

```python
import requests

annotation_data = {
    "dataset_name": "your_dataset",
    "image_filename": "table_1.png",
    "annotator": "system",
    "table_structure": json.dumps(structure_info),
    "table_content": json.dumps(json_schema_data),
    "annotation_type": "json_schema"
}

response = requests.post("/api/annotations", json=annotation_data)
```

### 4. 准确率计算

系统会自动使用JSON Schema数据进行准确率计算：

```javascript
// 在analyzer中，系统会自动检测并使用JSON Schema数据
const accuracyData = calculateAccuracyForCase(
    caseData, 
    annotationData, 
    useAnnotationAsBaseline,
    jsonSchemaData  // 新增的JSON Schema数据参数
);
```

## API 更新

### 新增端点

#### 获取数据集的JSON Schema数据
```
GET /api/datasets/{dataset_name}/json-schema-data
```

返回指定数据集的JSON Schema格式数据数组。

### 更新的端点

#### 创建标注
```
POST /api/annotations
```

现在支持 `annotation_type: "json_schema"` 和JSON Schema格式的 `table_content`。

## 前端更新

### TableRenderer组件

现在支持渲染JSON Schema格式的表格：

```jsx
<TableRenderer
  content={jsonSchemaData}
  type="json_schema"
  placeholder="无JSON Schema数据"
/>
```

### CaseDetail组件

添加了JSON Schema数据的显示和基准选择：

- 自动检测JSON Schema格式的标注数据
- 提供JSON Schema数据作为准确率计算基准的选项
- 显示JSON Schema格式的表格渲染结果

## 测试

运行集成测试：

```bash
python test_json_schema_integration.py
```

测试包括：
- JSON Schema数据生成
- 数据解析
- 准确率计算
- API集成

## 兼容性

系统保持向后兼容：

1. **现有数据**：传统的HTML/Markdown格式标注数据仍然有效
2. **渐进升级**：可以逐步将数据集迁移到JSON Schema格式
3. **自动检测**：系统会自动检测数据格式并使用相应的处理逻辑

## 优势

### 1. 更精确的准确率计算
- 基于结构化数据进行比较
- 支持复杂内容类型的精确匹配
- 考虑表格结构（合并单元格等）

### 2. 更丰富的内容支持
- 复选框状态的准确识别
- 列表项的精确匹配
- 合并单元格的正确处理

### 3. 更好的可扩展性
- 标准化的数据格式
- 易于添加新的内容类型
- 便于数据交换和处理

## 故障排除

### 常见问题

1. **JSON Schema数据不显示**
   - 检查数据格式是否正确
   - 确认API端点返回有效数据
   - 查看浏览器控制台错误信息

2. **准确率计算异常**
   - 验证JSON Schema数据结构
   - 检查数据类型匹配
   - 查看后端日志

3. **数据库迁移失败**
   - 确认数据库连接正常
   - 检查用户权限
   - 查看错误日志

### 调试技巧

1. 使用浏览器开发者工具查看网络请求
2. 检查后端日志文件
3. 运行测试脚本验证各个组件
4. 使用JSON验证工具检查数据格式

## 下一步

1. **性能优化**：针对大量JSON Schema数据的处理优化
2. **更多内容类型**：添加图片、链接等内容类型支持
3. **批量处理**：支持批量转换现有数据到JSON Schema格式
4. **可视化工具**：开发JSON Schema数据的可视化编辑器
