"""
表格数据解析器

用于将HTML表格数据转换为标注系统所需的格式
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup
import pandas as pd


class TableDataParser:
    """表格数据解析器"""
    
    def __init__(self):
        self.soup = None
        
    def parse_html_table(self, html_content: str) -> Optional[Dict]:
        """
        解析HTML表格内容

        Args:
            html_content: HTML内容

        Returns:
            Dict: 包含表格结构和内容的字典
        """
        try:
            self.soup = BeautifulSoup(html_content, 'html.parser')

            # 查找表格
            table = self.soup.find('table')
            if not table:
                print("未找到表格元素")
                return None

            # 解析表格数据和结构信息
            table_info = self._extract_table_data(table)
            if not table_info:
                return None

            # 生成表格结构信息
            structure = self._generate_table_structure(table_info)

            # 保留原始HTML内容，确保所有格式信息不丢失
            # 提取完整的表格HTML，包括样式
            original_html = str(table)

            # 如果原始HTML中包含完整的HTML文档结构，保留样式信息
            style_tag = self.soup.find('style')
            if style_tag:
                # 将样式信息包含在表格HTML中
                original_html = f"<style>{style_tag.get_text()}</style>\n{original_html}"

            return {
                'table_structure': json.dumps(structure, ensure_ascii=False),
                'table_content': original_html,  # 保留原始HTML而不是重新生成
                'raw_data': table_info
            }

        except Exception as e:
            print(f"解析HTML表格失败: {e}")
            return None
    
    def _extract_table_data(self, table) -> Optional[Dict]:
        """提取表格数据和结构信息"""
        try:
            # 存储表格的完整结构信息
            table_info = {
                'rows': [],
                'merged_cells': [],
                'nested_tables': [],
                'cell_styles': {},
                'table_attributes': {}
            }

            # 提取表格属性
            if table.attrs:
                table_info['table_attributes'] = dict(table.attrs)

            # 处理所有行
            all_rows = table.find_all('tr')
            row_index = 0

            for tr in all_rows:
                row_data = []
                col_index = 0

                for cell in tr.find_all(['th', 'td']):
                    # 提取单元格文本
                    cell_text = cell.get_text(strip=True)

                    # 提取单元格属性
                    cell_info = {
                        'text': cell_text,
                        'tag': cell.name,
                        'rowspan': int(cell.get('rowspan', 1)),
                        'colspan': int(cell.get('colspan', 1)),
                        'attributes': dict(cell.attrs) if cell.attrs else {}
                    }

                    # 检查是否包含嵌套表格
                    nested_table = cell.find('table')
                    if nested_table:
                        # 递归处理嵌套表格
                        nested_info = self._extract_table_data(nested_table)
                        if nested_info:
                            cell_info['nested_table'] = nested_info
                            table_info['nested_tables'].append({
                                'row': row_index,
                                'col': col_index,
                                'table_data': nested_info
                            })

                    # 记录合并单元格信息
                    if cell_info['rowspan'] > 1 or cell_info['colspan'] > 1:
                        table_info['merged_cells'].append({
                            'row': row_index,
                            'col': col_index,
                            'rowspan': cell_info['rowspan'],
                            'colspan': cell_info['colspan']
                        })

                    # 记录样式信息
                    if 'style' in cell.attrs:
                        table_info['cell_styles'][f"{row_index},{col_index}"] = cell.attrs['style']

                    row_data.append(cell_info)
                    col_index += 1

                if row_data:
                    table_info['rows'].append(row_data)
                    row_index += 1

            return table_info if table_info['rows'] else None

        except Exception as e:
            print(f"提取表格数据失败: {e}")
            return None
    
    def _generate_table_structure(self, table_info: Dict) -> Dict:
        """生成表格结构信息"""
        if not table_info or not table_info.get('rows'):
            return {}

        rows_data = table_info['rows']
        rows = len(rows_data)
        cols = max(len(row) for row in rows_data) if rows_data else 0

        # 分析表头 - 检查第一行是否包含th标签
        has_header = False
        headers = []
        if rows_data:
            first_row = rows_data[0]
            has_header = any(cell.get('tag') == 'th' for cell in first_row)
            headers = [cell.get('text', '') for cell in first_row]

        # 分析数据类型
        data_types = []
        if len(rows_data) > 1:
            for col_idx in range(cols):
                col_data = []
                start_row = 1 if has_header else 0
                for row_idx in range(start_row, rows):
                    if col_idx < len(rows_data[row_idx]):
                        cell_text = rows_data[row_idx][col_idx].get('text', '')
                        col_data.append(cell_text)

                # 推断数据类型
                data_type = self._infer_data_type(col_data)
                data_types.append(data_type)

        # 构建完整的结构信息
        structure = {
            'rows': rows,
            'cols': cols,
            'headers': headers,
            'data_types': data_types,
            'has_header': has_header,
            'merged_cells': table_info.get('merged_cells', []),
            'nested_tables': table_info.get('nested_tables', []),
            'cell_styles': table_info.get('cell_styles', {}),
            'table_attributes': table_info.get('table_attributes', {}),
            'table_type': 'complex_table' if table_info.get('merged_cells') or table_info.get('nested_tables') else 'data_table'
        }

        return structure
    
    def _infer_data_type(self, col_data: List[str]) -> str:
        """推断列数据类型"""
        if not col_data:
            return 'string'
            
        # 统计不同类型的数量
        numeric_count = 0
        date_count = 0
        
        for value in col_data:
            value = value.strip()
            if not value:
                continue
                
            # 检查是否为数字
            if re.match(r'^-?\d+\.?\d*$', value):
                numeric_count += 1
            # 检查是否为日期
            elif re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', value):
                date_count += 1
            # 检查是否为百分比
            elif value.endswith('%'):
                numeric_count += 1
        
        total_count = len([v for v in col_data if v.strip()])
        if total_count == 0:
            return 'string'
            
        # 如果超过70%是数字，认为是数字类型
        if numeric_count / total_count > 0.7:
            return 'number'
        # 如果超过50%是日期，认为是日期类型
        elif date_count / total_count > 0.5:
            return 'date'
        else:
            return 'string'
    
    def _generate_html_table(self, table_info: Dict) -> str:
        """生成HTML格式的表格（保留结构信息）"""
        if not table_info or not table_info.get('rows'):
            return "<table></table>"

        html_lines = ['<table']

        # 添加表格属性
        table_attrs = table_info.get('table_attributes', {})
        for attr, value in table_attrs.items():
            html_lines[0] += f' {attr}="{value}"'
        html_lines[0] += '>'

        rows_data = table_info['rows']

        # 检查是否有表头
        has_header = any(cell.get('tag') == 'th' for cell in rows_data[0]) if rows_data else False

        if has_header and rows_data:
            # 处理表头
            html_lines.append('  <thead>')
            html_lines.append('    <tr>')
            for col_idx, cell in enumerate(rows_data[0]):
                cell_html = f'      <th'

                # 添加合并单元格属性
                if cell.get('rowspan', 1) > 1:
                    cell_html += f' rowspan="{cell["rowspan"]}"'
                if cell.get('colspan', 1) > 1:
                    cell_html += f' colspan="{cell["colspan"]}"'

                # 添加其他属性
                for attr, value in cell.get('attributes', {}).items():
                    if attr not in ['rowspan', 'colspan']:
                        cell_html += f' {attr}="{value}"'

                cell_html += f'>{self._escape_html(cell.get("text", ""))}</th>'
                html_lines.append(cell_html)
            html_lines.append('    </tr>')
            html_lines.append('  </thead>')

        # 处理数据行
        start_row = 1 if has_header else 0
        if len(rows_data) > start_row:
            html_lines.append('  <tbody>')
            for row_idx in range(start_row, len(rows_data)):
                row = rows_data[row_idx]
                html_lines.append('    <tr>')
                for col_idx, cell in enumerate(row):
                    tag = cell.get('tag', 'td')
                    cell_html = f'      <{tag}'

                    # 添加合并单元格属性
                    if cell.get('rowspan', 1) > 1:
                        cell_html += f' rowspan="{cell["rowspan"]}"'
                    if cell.get('colspan', 1) > 1:
                        cell_html += f' colspan="{cell["colspan"]}"'

                    # 添加其他属性
                    for attr, value in cell.get('attributes', {}).items():
                        if attr not in ['rowspan', 'colspan']:
                            cell_html += f' {attr}="{value}"'

                    # 处理嵌套表格
                    if 'nested_table' in cell:
                        nested_html = self._generate_html_table(cell['nested_table'])
                        cell_html += f'>{nested_html}</{tag}>'
                    else:
                        cell_html += f'>{self._escape_html(cell.get("text", ""))}</{tag}>'

                    html_lines.append(cell_html)
                html_lines.append('    </tr>')
            html_lines.append('  </tbody>')

        html_lines.append('</table>')
        return '\n'.join(html_lines)

    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符"""
        if not text:
            return ""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def parse_generated_data(self, gen_data_dir: str, image_filename: str) -> Optional[Dict]:
        """
        解析生成的表格数据
        
        Args:
            gen_data_dir: 生成数据目录
            image_filename: 图片文件名
            
        Returns:
            Dict: 解析结果
        """
        import os
        
        # 根据图片文件名推断HTML文件名
        base_name = os.path.splitext(image_filename)[0]
        html_filename = f"{base_name}.html"
        html_path = os.path.join(gen_data_dir, html_filename)
        
        if not os.path.exists(html_path):
            print(f"HTML文件不存在: {html_path}")
            return None
            
        try:
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            return self.parse_html_table(html_content)
            
        except Exception as e:
            print(f"读取HTML文件失败: {e}")
            return None

    def parse_json_schema_data(self, json_data: Dict) -> Optional[Dict]:
        """
        解析JSON Schema格式的表格数据

        Args:
            json_data: JSON Schema格式的表格数据

        Returns:
            Dict: 解析结果，包含table_structure和table_content
        """
        try:
            if not json_data or not isinstance(json_data, dict):
                print("无效的JSON数据")
                return None

            # 查找表格元素
            elements = json_data.get('elements', [])
            table_element = None

            for element in elements:
                if element.get('type') == 'table':
                    table_element = element
                    break

            if not table_element:
                print("未找到表格元素")
                return None

            rows = table_element.get('rows', [])
            if not rows:
                print("表格无行数据")
                return None

            # 分析表格结构
            structure = self._analyze_json_schema_structure(rows)

            # 将JSON Schema数据转换为标准格式
            table_content = json.dumps(json_data, ensure_ascii=False)

            return {
                'table_structure': json.dumps(structure, ensure_ascii=False),
                'table_content': table_content,
                'raw_data': json_data
            }

        except Exception as e:
            print(f"解析JSON Schema数据失败: {e}")
            return None

    def _analyze_json_schema_structure(self, rows: list) -> Dict:
        """
        分析JSON Schema表格的结构

        Args:
            rows: 表格行数据

        Returns:
            Dict: 表格结构信息
        """
        if not rows:
            return {}

        row_count = len(rows)
        max_cols = 0
        merged_cells = []
        complex_cells = []

        for row_idx, row in enumerate(rows):
            if not isinstance(row, list):
                continue

            col_count = 0
            for col_idx, cell in enumerate(row):
                if not isinstance(cell, dict):
                    col_count += 1
                    continue

                # 处理合并单元格
                colspan = cell.get('colspan', 1)
                rowspan = cell.get('rowspan', 1)
                col_count += colspan

                if colspan > 1 or rowspan > 1:
                    merged_cells.append({
                        'row': row_idx,
                        'col': col_idx,
                        'rowspan': rowspan,
                        'colspan': colspan
                    })

                # 处理复杂内容
                content = cell.get('content')
                if isinstance(content, dict):
                    complex_cells.append({
                        'row': row_idx,
                        'col': col_idx,
                        'type': content.get('type', 'unknown'),
                        'content': content
                    })

            max_cols = max(max_cols, col_count)

        return {
            'rows': row_count,
            'cols': max_cols,
            'merged_cells': merged_cells,
            'complex_cells': complex_cells,
            'table_type': 'json_schema',
            'has_complex_content': len(complex_cells) > 0
        }
